
<!doctype html>
<html lang="en" class="semi-dark">

<head>
	<!-- Required meta tags -->
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

	<!--favicon-->
	<link rel="icon" href="<?php echo e(asset('assets/images/favicon-32x32.png')); ?>" type="image/png" />
	<!--plugins-->
	<link href="<?php echo e(asset('assets/plugins/simplebar/css/simplebar.css')); ?>" rel="stylesheet" />
	<link href="<?php echo e(asset('assets/plugins/perfect-scrollbar/css/perfect-scrollbar.css')); ?>" rel="stylesheet" />
	<link href="<?php echo e(asset('assets/plugins/metismenu/css/metisMenu.min.css')); ?>" rel="stylesheet" />
	<!-- loader-->
	<link href="<?php echo e(asset('assets/css/pace.min.css')); ?>" rel="stylesheet" />
	<script src="<?php echo e(asset('assets/js/pace.min.js')); ?>"></script>
	<!-- Bootstrap CSS -->
	<link href="<?php echo e(asset('assets/css/bootstrap.min.css')); ?>" rel="stylesheet">
	<link href="<?php echo e(asset('assets/css/bootstrap-extended.css')); ?>" rel="stylesheet">
	<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap" rel="stylesheet">
	<link href="<?php echo e(asset('assets/css/app.css')); ?>" rel="stylesheet">
	<link href="<?php echo e(asset('assets/css/icons.css')); ?>" rel="stylesheet">
	<title>Jualinn</title>
</head>

<body class="bg-login">
	<!--wrapper-->
	<div class="wrapper">
		<div class="section-authentication-signin d-flex align-items-center justify-content-center my-5 my-lg-0">
			<div class="container">
				<div class="row row-cols-1 row-cols-lg-2 row-cols-xl-3">
					<div class="col mx-auto">
						<div class="card mb-0">
							<div class="card-body">
								<div class="p-4">
									<div class="mb-3 text-center">
										<img src="<?php echo e(asset('assets/images/logo-img.png')); ?>" width="125" alt="" />
									</div>
									<div class="text-center mb-4">
										
										<p class="mb-0">Please log in to your account</p>
									</div>
									<div class="form-body">
										<form class="row g-3 form-login">
											<div class="col-12">
												<label for="username" class="form-label">Email</label>
												<input type="email" class="form-control" name="username" id="username" placeholder="<EMAIL>">
											</div>
											<div class="col-12">
												<label for="password" class="form-label">Password</label>
												<div class="input-group" id="show_hide_password">
													<input type="password" class="form-control border-end-0" name="password" id="password" value="" placeholder="Enter Password"> <a href="javascript:;" class="input-group-text bg-transparent"><i class='bx bx-hide'></i></a>
												</div>
											</div>
											<div class="col-md-8">
												<div class="form-check form-switch">
													<input class="form-check-input" type="checkbox" id="flexSwitchCheckChecked">
													<label class="form-check-label" for="flexSwitchCheckChecked">Remember Me</label>
												</div>
											</div>
											<div class="col-12">
												<div class="d-grid">
                                                    <input onclick="login()" class="btn btn-block btn-primary" type="button" value="Sign In" id="btn-login">
													

												</div>
											</div>
										</form>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!--end row-->
			</div>
		</div>
	</div>
	<!--end wrapper-->
	<!-- Bootstrap JS -->
	<script src="<?php echo e(asset('assets/js/bootstrap.bundle.min.js')); ?>"></script>
	<!--plugins-->
	<script src="<?php echo e(asset('assets/js/jquery.min.js')); ?>"></script>
	<script src="<?php echo e(asset('assets/plugins/simplebar/js/simplebar.min.js')); ?>"></script>
	<script src="<?php echo e(asset('assets/plugins/metismenu/js/metisMenu.min.js')); ?>"></script>
	<script src="<?php echo e(asset('assets/plugins/perfect-scrollbar/js/perfect-scrollbar.js')); ?>"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.21.0/jquery.validate.min.js" integrity="sha512-KFHXdr2oObHKI9w4Hv1XPKc898mE4kgYx58oqsc/JqqdLMDI4YjOLzom+EMlW8HFUd0QfjfAvxSL6sEq/a42fQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/2.1.2/sweetalert.min.js" integrity="sha512-AA1Bzp5Q0K1KanKKmvN/4d3IRKVlv9PYgwFPvm32nPO6QS8yH1HO7LbgB1pgiOxPtfeg5zEn2ba64MUcqJx6CA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    <!--Password show & hide js -->
	<script>
		$(document).ready(function () {
			$("#show_hide_password a").on('click', function (event) {
				event.preventDefault();
				if ($('#show_hide_password input').attr("type") == "text") {
					$('#show_hide_password input').attr('type', 'password');
					$('#show_hide_password i').addClass("bx-hide");
					$('#show_hide_password i').removeClass("bx-show");
				} else if ($('#show_hide_password input').attr("type") == "password") {
					$('#show_hide_password input').attr('type', 'text');
					$('#show_hide_password i').removeClass("bx-hide");
					$('#show_hide_password i').addClass("bx-show");
				}
			});

            // Initialize PerfectScrollbar if the element exists
            // var psElement = document.querySelector('.your-scrollbar-element');
            // if (psElement) {
            //     new PerfectScrollbar(psElement);
            // }
		});
	</script>
    <script type="text/javascript">
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  var try_login = 0;
  var max_failed_login = 3;
  var time_lock = 10;
	$('#username').keypress(function (e) {
	 var key = e.which;
	 if(key == 13)  // the enter key code
		{
			login();
		}
	});
	$('#password').keypress(function (e) {
	 var key = e.which;
	 if(key == 13)  // the enter key code
		{
			login();
		}
	});
  function login(){
      try_login ++; // INCREMENT TRY LOGIN

      var data  = new FormData($('.form-login')[0]);
      data.append('try_login', try_login);
      data.append('max_failed_login', max_failed_login);
      data.append('time_lock', time_lock);
      $.ajax({
          url: "<?php echo e(route('doLogin')); ?>",
          type: 'POST',
          data: data,
          async: true,
          cache: false,
          contentType: false,
          processData: false
      }).done(function(data){
          $('.form-login').validate(data, 'has-error');
          if(data.status == 'success'){
						swal('Selamat Datang !', data.message, 'success');
            window.location.replace("<?php echo url($data['next_url']); ?>");
          } else if(data.status == 'error') {
              $('#btn-login').html('Login').removeAttr('disabled');
              swal('Whoops !', data.message, 'warning');
              if (try_login >= max_failed_login) {
                  signin_locked();
              }
          } else {
              var n = 0;
              for(key in data){
              if (n == 0) {var dt0 = key;}
              n++;
          }
          $('#btn-login').html('Login').removeAttr('disabled');
          swal('Whoops !', 'Kolom '+dt0+' Tidak Boleh Kosong !!', 'error');
      }
      }).fail(function() {
              swal("MAAF !","Terjadi Kesalahan, Silahkan Ulangi Kembali !!", "warning");
              $('#btn-login').html('Login').removeAttr('disabled');
      });
  }

  function signin_locked() {
      $('#btn-login').attr('disabled', true);
      $('#btn-login').val(`Terbuka dalam ${time_lock} detik`);

      // Update the count down every 1 second
      var count = time_lock
      var x = setInterval(function() {

          count -= 1;

          $('#btn-login').val(`Terbuka dalam ${count} detik`);

          // If the count down is over, write some text
          if (count <= 0) {
              clearInterval(x);
              try_login = 0;

              $('#btn-login').attr('disabled', false);
              $('#btn-login').val(`Sign In`);

          }
      }, 1000);
  }
  </script>
	<!--app JS-->
	
</body>

</html>
<?php /**PATH C:\laragon\www\jualinn\resources\views/auth/login.blade.php ENDPATH**/ ?>