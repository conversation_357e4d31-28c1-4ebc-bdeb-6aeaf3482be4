<div class="tab-pane fade" id="pricing" role="tabpanel">
    <div class="card mb-3">
        <div class="card-header bg-light">
            <h5 class="mb-0">Pengaturan Harga Produk</h5>
        </div>
        <div class="card-body">
            <!-- Price Type Selection -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="nav nav-pills nav-fill" id="price-type-tab">
                        <button type="button" class="nav-link active" id="simple-price-tab-btn">
                            <i class='bx bx-purchase-tag me-1'></i> Harga Simple
                            <small class="d-block text-muted mt-1">Produk dengan satu harga</small>
                        </button>
                        <button type="button" class="nav-link" id="variant-price-tab-btn">
                            <i class='bx bx-purchase-tag-alt me-1'></i> Harga Variable
                            <small class="d-block text-muted mt-1">Produk dengan berbagai variasi (ukuran, warna, dll)</small>
                        </button>
                        <div class="alert alert-info mt-2 mb-0 w-100" id="price-type-info" style="display: none;">
                            <i class='bx bx-info-circle me-1'></i>
                            <span id="price-type-info-text"></span>
                        </div>
                    </div>
                    <input type="hidden" id="has_variants_hidden" name="has_variants" value="<?php echo e(old('has_variants', isset($product) && $product->has_variants ? '1' : '0')); ?>">
                </div>
            </div>

            <!-- Price Panels -->
            <div class="tab-content" id="price-tab-content">
                <!-- Simple Price Panel -->
                <div class="tab-pane fade show active" id="simple-price-panel">
                    <div class="card border shadow-sm mb-3">
                        <div class="card-header bg-white">
                            <h6 class="mb-0">Harga Dasar</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label" for="regular_price">Harga Normal <span class="text-danger">*</span></label>
                                    <div class="input-group <?php $__errorArgs = ['regular_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> has-validation <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                        <span class="input-group-text">Rp</span>
                                        <input type="text" class="form-control price-input <?php $__errorArgs = ['regular_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="regular_price" name="regular_price" value="<?php echo e(old('regular_price', isset($product) ? $product->regular_price : '')); ?>" min="0" required>
                                        <?php $__errorArgs = ['regular_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <small class="text-muted">Harga jual normal produk ini</small>
                                </div>

                                <div class="col-md-6">
                                    <label class="form-label" for="cost_price">Harga Pokok (HPP)</label>
                                    <div class="input-group <?php $__errorArgs = ['cost_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> has-validation <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                        <span class="input-group-text">Rp</span>
                                        <input type="text" class="form-control price-input <?php $__errorArgs = ['cost_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="cost_price" name="cost_price" value="<?php echo e(old('cost_price', isset($product) ? $product->cost_price : '')); ?>" min="0">
                                        <?php $__errorArgs = ['cost_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <small class="text-muted">Modal atau harga pokok pembelian produk</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Promo Price Panel -->
                    <div class="card border shadow-sm mb-3">
                        <div class="card-header bg-white d-flex align-items-center">
                            <div class="form-check form-switch mb-0">
                                <input type="checkbox" class="form-check-input" id="has_promo" name="has_promo" value="1" <?php echo e(old('has_promo', isset($product) && $product->has_promo ? '1' : '0') ? 'checked' : ''); ?>>
                                <label class="form-check-label fw-bold" for="has_promo">Harga Promo (Diskon)</label>
                            </div>
                        </div>
                        <div class="card-body" id="promo-price-container" style="<?php echo e(old('has_promo', isset($product) && $product->has_promo ? '1' : '0') ? '' : 'display: none;'); ?>">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label" for="discount_price">Harga Promo</label>
                                    <div class="input-group <?php $__errorArgs = ['discount_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> has-validation <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                        <span class="input-group-text">Rp</span>
                                        <input type="text" class="form-control price-input <?php $__errorArgs = ['discount_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="discount_price" name="discount_price" value="<?php echo e(old('discount_price', isset($product) ? $product->discount_price : '')); ?>" min="0">
                                        <?php $__errorArgs = ['discount_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex h-100 align-items-center">
                                        <div class="discount-badge px-3 py-2 rounded bg-success-subtle text-success" id="discount-preview" style="display: none;">
                                            <i class='bx bx-tag-alt me-1'></i> <span id="discount-percentage">0%</span> OFF
                                        </div>
                                        <div class="alert alert-info px-3 py-2 mb-0 ms-2">
                                            <div class="d-flex">
                                                <div class="me-2"><i class='bx bx-info-circle fs-5'></i></div>
                                                <div>Harga promo akan ditampilkan sebagai diskon dari harga normal</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Wholesale Price Panel -->
                    <div class="card border shadow-sm mb-3">
                        <div class="card-header bg-white d-flex align-items-center">
                            <div class="form-check form-switch mb-0">
                                <input type="checkbox" class="form-check-input" id="has_wholesale" name="has_wholesale" value="1" <?php echo e(old('has_wholesale', isset($product) && $product->has_wholesale ? '1' : '0') ? 'checked' : ''); ?>>
                                <label class="form-check-label fw-bold" for="has_wholesale">Harga Grosir</label>
                            </div>
                        </div>
                        <div class="card-body" id="wholesaleContainer" style="<?php echo e(old('has_wholesale', isset($product) && $product->has_wholesale ? '1' : '0') ? '' : 'display: none;'); ?>">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">Daftar Harga Grosir</h6>
                                        <button type="button" id="add-wholesale" class="btn btn-primary btn-sm">
                                            <i class='bx bx-plus me-1'></i> Tambah Tingkat Harga
                                        </button>
                                    </div>
                                    <p class="text-muted small mb-0 mt-1">Tambahkan minimal quantity dan harga untuk setiap tingkat harga grosir</p>
                                </div>
                            </div>

                            <div id="empty-wholesale-state" class="text-center py-4 border-dashed rounded bg-light mb-3">
                                <div class="mb-2">
                                    <i class='bx bx-package' style="font-size: 2.5rem; color: #d9dee3;"></i>
                                </div>
                                <h6 class="text-muted">Belum ada harga grosir</h6>
                                <p class="text-muted small mb-0">Klik "Tambah Tingkat Harga" untuk menambahkan harga grosir</p>
                            </div>

                            <div id="wholesaleList" class="wholesale-tiers">
                                <!-- Dynamic content will appear here -->
                            </div>

                            <div class="card border-0 bg-light mt-3">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="me-3">
                                            <i class='bx bx-info-circle fs-4 text-primary'></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-2">Cara Kerja Harga Grosir</h6>
                                            <ol class="text-muted small mb-0">
                                                <li>Pelanggan menambahkan produk ke keranjang</li>
                                                <li>Saat jumlah mencapai batas minimal tier, harga secara otomatis berubah</li>
                                                <li>Semakin banyak dibeli, semakin besar diskon yang didapat</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Variant Price Panel -->
                <div class="tab-pane fade" id="variant-price-panel">
                    <!-- Attribute Selection - Improved Design -->
                    <div class="card border shadow-sm mb-3">
                        <div class="card-header bg-white d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <h6 class="mb-0">Langkah 1: Pilih Atribut Variasi</h6>
                                <span class="badge bg-primary ms-2">Diperlukan</span>
                            </div>
                            <button class="btn btn-outline-primary btn-sm" type="button" id="help-attribute-btn">
                                <i class='bx bx-help-circle me-1'></i> Bantuan
                            </button>
                        </div>
                        <div class="card-body">
                            <!-- Attribute Selection Guide -->
                            <div class="alert alert-info d-flex align-items-center mb-3" role="alert">
                                <i class='bx bx-info-circle me-2 fs-5'></i>
                                <div>
                                    <strong>Petunjuk:</strong> Pilih atribut (seperti warna, ukuran) lalu tambahkan nilai-nilai untuk setiap atribut.
                                </div>
                            </div>

                            <div id="attribute-manager" class="mb-3">
                                <label for="attribute-main-select" class="form-label">Pilih atau buat atribut baru:</label>
                                <div class="input-group">
                                    <select class="form-select attribute-main-select" id="attribute-main-select">
                                        <option value="">+ Tambah Atribut Baru</option>
                                        <?php $__currentLoopData = $attributes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attribute): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($attribute->id); ?>" data-name="<?php echo e($attribute->name); ?>"><?php echo e($attribute->name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <option value="custom">Buat Atribut Kustom...</option>
                                    </select>
                                    <button class="btn btn-primary" type="button" id="add-attribute-btn">
                                        <i class='bx bx-plus'></i> Tambah
                                    </button>
                                </div>
                                <div class="form-text">Contoh atribut: Warna, Ukuran, Material, Model, dll.</div>
                            </div>

                            <!-- Attribute Cards Container -->
                            <div id="attribute-cards" class="mb-3">
                                <!-- Attribute cards will be added here dynamically -->
                            </div>

                            <!-- Hidden inputs for variant attributes and values -->
                            <div id="variant-hidden-inputs">
                                <?php if(isset($product) && $product->has_variants && isset($product->variant_attributes)): ?>
                                    <?php $__currentLoopData = $product->variant_attributes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attribute): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <input type="hidden" name="variant_attributes[]" value="<?php echo e($attribute); ?>">
                                        <?php if(isset($product->variant_values[$attribute])): ?>
                                            <?php $__currentLoopData = $product->variant_values[$attribute]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <input type="hidden" name="variant_values[<?php echo e($attribute); ?>][]" value="<?php echo e($value); ?>">
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </div>

                            <!-- Empty State with Examples -->
                            <div id="empty-attributes-state" class="text-center py-4 border-dashed rounded bg-light">
                                <div class="mb-2">
                                    <i class='bx bx-layer-plus' style="font-size: 2.5rem; color: #d9dee3;"></i>
                                </div>
                                <h6 class="text-muted">Belum ada atribut</h6>
                                <p class="text-muted small mb-2">Pilih atribut dari dropdown di atas untuk mulai membuat variasi produk</p>

                                <div class="row justify-content-center mt-3">
                                    <div class="col-md-8">
                                        <div class="card bg-white">
                                            <div class="card-header py-2 px-3">
                                                <h6 class="mb-0 small">Contoh Atribut</h6>
                                            </div>
                                            <div class="card-body py-2 px-3">
                                                <div class="row">
                                                    <div class="col-6">
                                                        <p class="mb-1 small fw-bold">Warna:</p>
                                                        <p class="small text-muted">Merah, Hitam, Biru</p>
                                                    </div>
                                                    <div class="col-6">
                                                        <p class="mb-1 small fw-bold">Ukuran:</p>
                                                        <p class="small text-muted">S, M, L, XL</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Attribute Summary & Variants Generation - Improved Design -->
                    <div class="card border shadow-sm mb-3">
                        <div class="card-header bg-white d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <h6 class="mb-0">Langkah 2: Buat Kombinasi Variasi</h6>
                                <span class="badge bg-primary ms-2">Diperlukan</span>
                            </div>
                            <div>
                                <span class="badge bg-info me-2">Total kombinasi: <span id="total-combinations">0</span></span>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Attribute Summary -->
                            <div id="attribute-summary" class="mb-3 p-3 border rounded bg-light">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="mb-0">Ringkasan Atribut</h6>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="preview-combinations">
                                        <label class="form-check-label small" for="preview-combinations">Tampilkan preview kombinasi</label>
                                    </div>
                                </div>

                                <div id="attribute-summary-content" class="mb-3">
                                    <!-- Summary content will be added here -->
                                    <p class="text-muted small mb-0" id="no-attributes-message">Belum ada atribut yang dipilih. Silakan tambahkan atribut pada langkah sebelumnya.</p>
                                </div>

                                <!-- Preview of combinations -->
                                <div id="combinations-preview" class="mb-3 border-top pt-3" style="display: none;">
                                    <h6 class="mb-2">Preview Kombinasi</h6>
                                    <div class="combinations-preview-content small">
                                        <!-- Preview content will be added here -->
                                    </div>
                                    <div class="text-center mt-2">
                                        <button type="button" class="btn btn-sm btn-outline-secondary" id="show-more-combinations">
                                            Tampilkan lebih banyak
                                        </button>
                                    </div>
                                </div>

                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" id="generate-variants-from-summary">
                                        <i class='bx bx-refresh me-1'></i> Generate Variasi
                                    </button>
                                </div>
                            </div>

                            <!-- Alert for large number of combinations -->
                            <div id="large-combinations-warning" class="alert alert-warning d-flex align-items-center" role="alert" style="display: none;">
                                <i class='bx bx-error-circle me-2 fs-5'></i>
                                <div>
                                    <strong>Perhatian!</strong> Anda akan membuat banyak kombinasi variasi. Pastikan semua kombinasi ini memang diperlukan.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Generated Variants Table - Improved Design -->
                    <div class="card border shadow-sm">
                        <div class="card-header bg-white d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <h6 class="mb-0">Langkah 3: Kelola Harga Variasi</h6>
                                <span class="badge bg-primary ms-2">Diperlukan</span>
                            </div>
                            <div class="btn-group">
                                <button type="button" class="btn btn-outline-primary btn-sm" id="copy-product-data" title="Salin harga produk ke semua variasi">
                                    <i class='bx bx-copy me-1'></i> Salin Harga Produk
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm" id="mass-update-variants">
                                    <i class='bx bx-edit me-1'></i> Update Massal
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" id="copy-variants" data-clipboard-target="#variants-data">
                                    <i class='bx bx-export me-1'></i> Export Data
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Quick Actions -->
                            <div class="alert alert-light border mb-3 d-flex align-items-center">
                                <i class='bx bx-bulb me-2 fs-5'></i>
                                <div>
                                    <strong>Tips:</strong> Gunakan tombol "Update Massal" untuk mengubah harga semua variasi sekaligus.
                                </div>
                            </div>

                            <!-- Hidden textarea for copying variants data -->
                            <textarea id="variants-data" style="position: absolute; left: -9999px;"></textarea>

                            <div class="table-responsive">
                                <table class="table table-bordered table-hover" id="variants-table">
                                    <thead class="table-light">
                                        <tr>
                                            <th style="width: 35%">Kombinasi</th>
                                            <th style="width: 20%">Kode Varian</th>
                                            <th style="width: 20%">Harga</th>
                                            <th style="width: 20%">Harga Promo</th>
                                            <th style="width: 5%">Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Variants will be added here -->
                                    </tbody>
                                </table>
                            </div>

                            <style>
                                /* Variant row styling */
                                .variant-row {
                                    transition: all 0.2s ease;
                                }

                                .variant-row:hover {
                                    background-color: rgba(105, 108, 255, 0.04);
                                }

                                .variant-combination-display {
                                    color: #566a7f;
                                }

                                .variant-row .form-control:focus {
                                    border-color: #696cff;
                                    box-shadow: 0 0 0 0.25rem rgba(105, 108, 255, 0.25);
                                }

                                /* Highlight newly added rows */
                                .variant-row.new-row {
                                    animation: highlight-row 2s ease;
                                }

                                @keyframes highlight-row {
                                    0% {
                                        background-color: rgba(105, 108, 255, 0.2);
                                    }

                                    100% {
                                        background-color: transparent;
                                    }
                                }
                            </style>

                            <!-- Empty State for Variants -->
                            <div id="empty-variants-state" class="text-center py-4 border-dashed rounded bg-light mt-3">
                                <div class="mb-2">
                                    <i class='bx bx-purchase-tag-alt' style="font-size: 2.5rem; color: #d9dee3;"></i>
                                </div>
                                <h6 class="text-muted">Belum ada variasi produk</h6>
                                <p class="text-muted small mb-0">Silakan lengkapi atribut pada langkah 1, kemudian klik "Generate Variasi" pada langkah 2</p>
                                <div class="mt-3">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="document.getElementById('attribute-main-select').focus()">
                                        <i class='bx bx-plus-circle me-1'></i> Mulai Tambah Atribut
                                    </button>
                                </div>
                            </div>

                            <!-- Variant Count Summary -->
                            <div id="variant-count-summary" class="mt-3 text-end" style="display: none;">
                                <span class="badge bg-secondary">Total Variasi: <span id="variant-count">0</span></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mass Update Modal -->
    <div class="modal fade" id="massUpdateModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Update Semua Variasi</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <div class="d-flex">
                            <div class="me-2"><i class='bx bx-info-circle fs-5'></i></div>
                            <div>Update semua variasi sekaligus dengan isian di bawah. Kosongkan field yang tidak ingin diubah.</div>
                        </div>
                    </div>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">Harga Semua Variasi</label>
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                <input type="text" class="form-control price-input" id="mass-price" min="0" placeholder="Kosongkan jika tidak diubah">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Harga Promo Semua Variasi</label>
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                <input type="text" class="form-control price-input" id="mass-discount-price" min="0" placeholder="Kosongkan jika tidak diubah">
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="generate-variant-codes">
                                <label class="form-check-label" for="generate-variant-codes">
                                    Generate kode varian otomatis
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-primary" id="apply-mass-update">Terapkan Ke Semua</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Help Modal for Attributes -->
    <div class="modal fade" id="attributeHelpModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Bantuan Atribut Produk</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <h6>Apa itu atribut produk?</h6>
                    <p class="text-muted small">Atribut produk adalah karakteristik yang membedakan varian produk Anda, seperti:</p>
                    <ul class="text-muted small">
                        <li><strong>Ukuran</strong>: S, M, L, XL</li>
                        <li><strong>Warna</strong>: Merah, Hitam, Biru</li>
                        <li><strong>Material</strong>: Katun, Polyester</li>
                        <li><strong>Model</strong>: A, B, C</li>
                    </ul>

                    <h6 class="mt-3">Cara Membuat Variasi:</h6>
                    <ol class="text-muted small">
                        <li>Pilih atribut dari dropdown (contoh: "Ukuran")</li>
                        <li>Tambahkan nilai-nilai untuk atribut tersebut (contoh: S, M, L)</li>
                        <li>Tambahkan atribut lain jika perlu (contoh: "Warna")</li>
                        <li>Klik "Generate Variasi" untuk membuat kombinasi</li>
                    </ol>

                    <div class="alert alert-info small mt-3">
                        <div class="d-flex">
                            <div class="me-2"><i class='bx bx-bulb fs-5'></i></div>
                            <div>
                                <strong>Tip:</strong> Jangan buat terlalu banyak kombinasi. Fokus pada variasi yang memang Anda jual.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Wholesale Template (Fixed) -->
    <script type="text/template" id="wholesale-row-template">
        <div class="wholesale-row card border mb-3 shadow-sm" data-id="{id}">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0 tier-label">Tingkat Harga {index}</h6>
                <button type="button" class="btn btn-outline-danger btn-sm delete-wholesale" title="Hapus">
                    <i class="bx bx-trash"></i>
                </button>
            </div>
            <div class="row g-3 align-items-center">
                <div class="col-md-6">
                    <label class="form-label">Jumlah Minimum</label>
                    <div class="input-group">
                        <input type="number" class="form-control wholesale-min-qty"
                            name="wholesale_prices[{index}][min_quantity]"
                            min="2" placeholder="Minimal 2" required>
                        <span class="input-group-text">pcs</span>
                    </div>
                    <small class="text-muted">Jumlah minimal untuk mendapatkan harga ini</small>
                </div>
                <div class="col-md-6">
                    <label class="form-label">Harga Per Unit</label>
                    <div class="input-group">
                        <span class="input-group-text">Rp</span>
                        <input type="text" class="form-control price-input wholesale-price"
                            name="wholesale_prices[{index}][price]"
                            min="0" placeholder="0" required>
                    </div>
                    <small class="text-muted">Harga per unit saat pembelian dalam jumlah ini</small>
                </div>
            </div>
        </div>
    </div>
</script>

</div>
<?php /**PATH C:\laragon\www\jualinn\resources\views/data-master/product/partials/pricing.blade.php ENDPATH**/ ?>