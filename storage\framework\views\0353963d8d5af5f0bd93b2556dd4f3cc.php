<script>
    $(document).ready(function() {
        // Check if product has wholesale prices
        <?php if(isset($product) && $product->has_wholesale): ?>
            // If product has wholesale, initialize toggle as ON
            $('#has_wholesale').prop('checked', true);
            $('#wholesaleContainer').show();

            // Load existing wholesale prices
            <?php $__currentLoopData = $product->wholesalePrices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $price): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                addWholesaleRowWithData({
                    id: <?php echo e($price->id); ?>,
                    min_quantity: <?php echo e($price->min_quantity); ?>,
                    price: <?php echo e($price->price); ?>

                });
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            // Update empty state
            updateWholesaleEmptyState();
        <?php else: ?>
            // Initialize wholesale toggle - default OFF (hidden by default like promo price)
            $('#has_wholesale').prop('checked', false);
            $('#wholesaleContainer').hide();

            // Clear any existing wholesale prices in form data when toggle is off
            clearWholesaleFormData();
        <?php endif; ?>

        // Wholesale toggle handler
        $('#has_wholesale').on('change', function() {
            if ($(this).is(':checked')) {
                $('#wholesaleContainer').slideDown();
                updateWholesaleEmptyState();
            } else {
                // Clear form data before hiding
                clearWholesaleFormData();
                $('#wholesaleContainer').slideUp();
            }
        });

        // Add wholesale row button
        $('#add-wholesale').on('click', function() {
            addWholesaleRow();
            updateWholesaleEmptyState();
        });

        // Delete wholesale row
        $(document).on('click', '.delete-wholesale', function() {
            const row = $(this).closest('.wholesale-row');
            const rowCount = $('.wholesale-row').length;

            Swal.fire({
                title: "Hapus Harga Grosir?",
                text: rowCount <= 1 ?
                    "Ini adalah harga grosir terakhir. Menghapusnya akan menonaktifkan fitur grosir." : "Anda yakin ingin menghapus harga grosir ini?",
                icon: "question",
                showCancelButton: true,
                confirmButtonText: "Ya, Hapus",
                cancelButtonText: "Batal",
                confirmButtonColor: "#d33",
                cancelButtonColor: "#3085d6",
            }).then((result) => {
                if (result.isConfirmed) {
                    row.fadeOut(300, function() {
                        $(this).remove();
                        renumberWholesaleRows();
                        updateWholesaleEmptyState();

                        // If no more rows left, uncheck the toggle
                        if ($('.wholesale-row').length === 0) {
                            $('#has_wholesale').prop('checked', false).trigger('change');
                        }
                    });
                }
            });
        });

        // Form submission handler to clean up data when wholesale is disabled
        $('form').on('submit', function() {
            if (!$('#has_wholesale').is(':checked')) {
                // Remove all wholesale price inputs from the form
                $('.wholesale-row').remove();
                $('input[name^="wholesale_prices"]').remove();
            }
        });

        // Function to clear wholesale form data
        function clearWholesaleFormData() {
            $('input[name^="wholesale_prices"]').remove();
        }

        // Function to add a new wholesale row
        function addWholesaleRow() {
            const rowCount = $('.wholesale-row').length;
            const rowId = Date.now();

            // Clone template and replace placeholders
            let template = $('#wholesale-row-template').html();
            template = template.replace(/{id}/g, rowId);
            template = template.replace(/{index}/g, rowCount);

            $('#wholesaleList').append(template);
            renumberWholesaleRows();

            // // Scroll to and highlight the new row
            // $('html, body').animate({
            //     scrollTop: $('.wholesale-row:last').offset().top - 100
            // }, 500);
            // highlightRow($('.wholesale-row:last'));
        }

        function addWholesaleRow() {
            const rowCount = $('.wholesale-row').length;
            const rowId = Date.now();

            // Ambil isi template dan ubah ke DOM element
            let template = $('#wholesale-row-template').html()
                .replace(/{id}/g, rowId)
                .replace(/{index}/g, rowCount);

            // Buat elemen dari string HTML
            const $newRow = $(template);

            // Tambahkan ke daftar
            $('#wholesaleList').append($newRow);

            // Renumber semua row
            renumberWholesaleRows();

            // Scroll dan highlight
            // $('html, body').animate({
            //     scrollTop: $newRow.offset().top - 100
            // }, 500);
            highlightRow($newRow);

            // Fokuskan ke input pertama agar bisa langsung diisi
            $newRow.find('input:visible:first').focus();

            return $newRow;
        }

        // Function to add a wholesale row with existing data
        function addWholesaleRowWithData(data) {
            const $newRow = addWholesaleRow();

            // Set the values
            if (data.id) {
                $newRow.attr('data-id', data.id);
                $newRow.append(`<input type="hidden" name="wholesale_prices[${$('.wholesale-row').length - 1}][id]" value="${data.id}">`);
            }

            $newRow.find('.wholesale-min-qty').val(data.min_quantity);
            $newRow.find('.wholesale-price').val(data.price);

            return $newRow;
        }


        // Function to renumber wholesale rows
        function renumberWholesaleRows() {
            $('.wholesale-row').each(function(index) {
                $(this).find('.tier-label').text('Tingkat Harga ' + (index + 1));
                $(this).find('.wholesale-min-qty').attr('name', `wholesale_prices[${index}][min_quantity]`);
                $(this).find('.wholesale-price').attr('name', `wholesale_prices[${index}][price]`);
            });
        }

        // Function to update empty state visibility
        function updateWholesaleEmptyState() {
            $('#empty-wholesale-state').toggle($('.wholesale-row').length === 0);
        }

        // Function to highlight a row temporarily
        function highlightRow(row) {
            row.addClass('border border-2 border-primary');
            setTimeout(function() {
                row.removeClass('border border-2 border-primary');
            }, 1500);
        }
    });
</script>
<?php /**PATH C:\laragon\www\jualinn\resources\views/data-master/product/scripts/product-wholesale.blade.php ENDPATH**/ ?>