<?php $__env->startSection('title', $title); ?>

<?php $__env->startSection('content'); ?>
    <h6 class="mb-0 text-uppercase"><?php echo e($title); ?></h6>
    <hr />

    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Daftar <?php echo e($title); ?></h5>
            <a href="<?php echo e(route('data-master.product.create')); ?>" class="btn btn-primary">
                <i class="bx bx-plus"></i> Tambah <?php echo e($title); ?>

            </a>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-3">
                    <label for="filter-status" class="form-label">Status</label>
                    <select id="filter-status" class="form-select">
                        <option value="">Semua Status</option>
                        <option value="active">Aktif</option>
                        <option value="draft">Tidak Aktif</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="filter-category" class="form-label">Kategori</label>
                    <select id="filter-category" class="form-select">
                        <option value="">Semua Kategori</option>
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($category->id); ?>"><?php echo e($category->name); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="filter-search" class="form-label">Pencarian</label>
                    <input type="text" id="filter-search" class="form-control" placeholder="Cari nama, kode produk...">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="button" class="btn btn-primary w-100" id="btn-filter">
                        <i class="bx bx-filter-alt"></i> Filter
                    </button>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table datatable table-striped" style="width:100%" id="data-table">
                    <thead>
                        <tr>
                            <th width="5%">No</th>
                            <th width="15%">Gambar</th>
                            <th width="25%">Nama Produk</th>
                            <th width="10%">Kode</th>
                            <th width="15%">Kategori</th>
                            <th width="10%">Varian</th>
                            <th width="15%">Harga</th>
                            <th width="10%">Status</th>
                            <th width="15%">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        $(document).ready(function() {
            let table = $('#data-table').DataTable({
                processing: true,
                serverSide: true,
                stateSave: true,
                deferRender: true,
                ajax: {
                    url: "<?php echo e(route('data-master.product.data')); ?>",
                    data: function(d) {
                        d.status = $('#filter-status').val();
                        d.category_id = $('#filter-category').val();
                        d.search = $('#filter-search').val();
                    }
                },
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false,
                        className: 'text-center'
                    },
                    {
                        data: 'image',
                        name: 'image',
                        orderable: false,
                        searchable: false,
                        className: 'text-center'
                    },
                    {
                        data: 'name',
                        name: 'name'
                    },
                    {
                        data: 'product_code',
                        name: 'product_code',
                        className: 'text-center'
                    },
                    {
                        data: 'category_name',
                        name: 'category_name'
                    },
                    {
                        data: 'variants_count',
                        name: 'has_variants',
                        className: 'text-center'
                    },
                    {
                        data: 'price',
                        name: 'regular_price',
                        className: 'text-end'
                    },
                    {
                        data: 'status_badge',
                        name: 'status',
                        className: 'text-center'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false,
                        className: 'text-center'
                    }
                ],
                order: [
                    [2, 'asc']
                ], // Order by product name
                language: {
                    processing: '<i class="bx bx-loader bx-spin font-medium-5 text-primary"></i><span class="ms-2">Memuat data...</span>',
                    search: "Cari:",
                    lengthMenu: "Tampilkan _MENU_ data",
                    info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                    infoEmpty: "Tidak ada data yang ditampilkan",
                    infoFiltered: "(difilter dari total _MAX_ data)",
                    loadingRecords: "Memuat data...",
                    zeroRecords: "Tidak ada data yang cocok",
                    emptyTable: "Tidak ada data tersedia",
                    paginate: {
                        first: "Pertama",
                        last: "Terakhir",
                        next: "Selanjutnya",
                        previous: "Sebelumnya"
                    },
                }
            });

            $('#btn-filter').click(function() {
                table.draw();
            });

            // Filter when pressing Enter in search field
            $('#filter-search').keypress(function(e) {
                if (e.which === 13) {
                    table.draw();
                }
            });

            // Handle delete button click
            $(document).on('click', '.btn-delete', function() {
                let id = $(this).data('id');
                Swal.fire({
                    title: 'Apakah Anda yakin?',
                    text: "Data yang dihapus tidak dapat dikembalikan!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Ya, hapus!',
                    cancelButtonText: 'Batal'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: "<?php echo e(route('data-master.product.destroy', '')); ?>/" + id,
                            type: 'DELETE',
                            data: {
                                _token: "<?php echo e(csrf_token()); ?>"
                            },
                            success: function(response) {
                                Swal.fire(
                                    'Terhapus!',
                                    response.message,
                                    'success'
                                );
                                table.draw();
                            },
                            error: function(xhr) {
                                Swal.fire(
                                    'Error!',
                                    xhr.responseJSON.message,
                                    'error'
                                );
                            }
                        });
                    }
                });
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('templates.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\jualinn\resources\views/data-master/product/index.blade.php ENDPATH**/ ?>