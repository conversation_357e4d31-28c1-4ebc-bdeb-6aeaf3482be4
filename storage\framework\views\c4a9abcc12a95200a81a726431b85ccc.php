<script>
    $(document).ready(function() {
        console.log('Product variants improvements script loaded');

        // Initialize the preview combinations toggle
        $('#preview-combinations').on('change', function() {
            if ($(this).is(':checked')) {
                updateCombinationsPreview();
                $('#combinations-preview').slideDown();
            } else {
                $('#combinations-preview').slideUp();
            }
        });

        // Show more combinations button
        $('#show-more-combinations').on('click', function() {
            const currentCount = $('.combinations-preview-content .combination-preview').length;
            updateCombinationsPreview(currentCount + 5);
        });

        // Add attribute button
        $('#add-attribute-btn').on('click', function() {
            const select = $('#attribute-main-select');
            const selectedValue = select.val();

            if (selectedValue) {
                // Trigger the change event on the select
                select.trigger('change');
            } else {
                // Show a message to select an attribute
                Swal.fire({
                    title: "Pi<PERSON>h Atribut",
                    text: "<PERSON>lakan pilih atribut dari dropdown terlebih dahulu.",
                    icon: "info",
                    confirmButtonText: "OK",
                });
            }
        });

        // Debug button for variants - add to the DOM
        // $('<button>')
        //     .attr('type', 'button')
        //     .addClass('btn btn-sm btn-outline-secondary mt-2')
        //     .attr('id', 'debug-variants-btn')
        //     .html('<i class="bx bx-bug-alt me-1"></i> Debug Variants')
        //     .css({
        //         position: 'fixed',
        //         bottom: '20px',
        //         right: '20px',
        //         zIndex: 1000,
        //         opacity: 0.7
        //     })
        //     .appendTo('body')
        //     .on('click', function() {
        //         debugVariants();
        //     });

        // // Debug function for variants
        // function debugVariants() {
        //     console.group('Variant Debug Information');

        //     // Log global variables
        //     console.log('Selected Attributes:', window.selectedAttributes);
        //     console.log('Attribute Values:', window.attributeValues);
        //     console.log('Attribute Value IDs:', window.attributeValueIds);

        //     // Log variant rows
        //     const variantRows = $('#variants-table tbody tr');
        //     console.log('Variant Rows Count:', variantRows.length);

        //     // Check each variant row
        //     variantRows.each(function(index) {
        //         const row = $(this);
        //         const rowId = row.attr('id') || `row-${index}`;
        //         console.group(`Variant Row: ${rowId}`);

        //         // Get combination
        //         const combinationInput = row.find('input[name^="variants"][name$="[combination]"]');
        //         let combination = {};
        //         if (combinationInput.length) {
        //             try {
        //                 combination = JSON.parse(combinationInput.val());
        //                 console.log('Combination:', combination);
        //             } catch (e) {
        //                 console.error('Error parsing combination:', e);
        //             }
        //         } else {
        //             console.warn('No combination input found');
        //         }

        //         // Get attribute value IDs
        //         const attributeValueIdInputs = row.find('input[name^="variants"][name$="[attribute_value_ids][]"]');
        //         const attributeValueIds = [];
        //         attributeValueIdInputs.each(function() {
        //             attributeValueIds.push($(this).val());
        //         });
        //         console.log('Attribute Value IDs:', attributeValueIds);

        //         // Get variant code
        //         const variantCode = row.find('.variant-code').val();
        //         console.log('Variant Code:', variantCode);

        //         // Get prices
        //         const price = row.find('.variant-price').val();
        //         const salePrice = row.find('.variant-sale-price').val();
        //         console.log('Price:', price);
        //         console.log('Sale Price:', salePrice);

        //         console.groupEnd();
        //     });

        //     console.groupEnd();

        //     // Show a message to check console
        //     Swal.fire({
        //         title: "Debug Info",
        //         text: "Variant debug information has been logged to the console. Press F12 to view.",
        //         icon: "info",
        //         confirmButtonText: "OK",
        //     });
        // }

        // Update the combinations preview
        function updateCombinationsPreview(maxCount = 5) {
            if (!$('#preview-combinations').is(':checked')) return;

            // Check if we have any attributes
            if (!selectedAttributes || selectedAttributes.length === 0) {
                $('#combinations-preview').hide();
                return;
            }

            // Check if all attributes have values
            let hasEmptyValues = false;
            for (const attr of selectedAttributes) {
                if (!attributeValues[attr] || attributeValues[attr].length === 0) {
                    hasEmptyValues = true;
                    break;
                }
            }

            if (hasEmptyValues) {
                $('#combinations-preview').hide();
                return;
            }

            // Generate all combinations
            const combinations = generateCombinations(attributeValues);

            if (combinations.length === 0) {
                $('#combinations-preview').hide();
                return;
            }

            // Show warning for large number of combinations
            if (combinations.length > 20) {
                $('#large-combinations-warning').show();
            } else {
                $('#large-combinations-warning').hide();
            }

            // Update the preview content
            let previewHtml = '';
            const displayCount = Math.min(combinations.length, maxCount);

            for (let i = 0; i < displayCount; i++) {
                const combination = combinations[i];
                const combinationString = Object.entries(combination).map(([attr, val]) => `${attr}: ${val}`).join(', ');

                previewHtml += `
                <div class="combination-preview p-2 mb-1 border rounded">
                    <i class='bx bx-tag-alt me-1'></i> ${combinationString}
                </div>`;
            }

            if (combinations.length > displayCount) {
                previewHtml += `<div class="text-center text-muted small mt-2">+ ${combinations.length - displayCount} kombinasi lainnya</div>`;
                $('#show-more-combinations').show();
            } else {
                $('#show-more-combinations').hide();
            }

            $('.combinations-preview-content').html(previewHtml);
            $('#combinations-preview').show();
        }

        // Helper function to generate all combinations of attributes
        function generateCombinations(attributeValues) {
            // Validate input
            if (!attributeValues || typeof attributeValues !== 'object') {
                return [];
            }

            const attrs = Object.keys(attributeValues);

            // Check if we have any attributes
            if (attrs.length === 0) {
                return [];
            }

            const result = [];

            function generateHelper(attrIndex, current) {
                if (attrIndex === attrs.length) {
                    // Make a deep copy of the current combination
                    result.push({
                        ...current
                    });
                    return;
                }

                const attr = attrs[attrIndex];
                const values = attributeValues[attr];

                // Check if values is an array and not empty
                if (!Array.isArray(values) || values.length === 0) {
                    return;
                }

                for (const val of values) {
                    // Create a new object for each branch to avoid reference issues
                    const newCurrent = {
                        ...current
                    };
                    newCurrent[attr] = val;
                    generateHelper(attrIndex + 1, newCurrent);
                }
            }

            generateHelper(0, {});
            return result;
        }

        // Override the updateAttributeSummary function to also update the combinations preview
        const originalUpdateAttributeSummary = ProductVariants.updateAttributeSummary;
        ProductVariants.updateAttributeSummary = function() {
            // Call the original function
            originalUpdateAttributeSummary.call(this);

            // Update the combinations preview
            updateCombinationsPreview();
        };

        // Override the updateVariantsEmptyState function to also update the variant count
        const originalUpdateVariantsEmptyState = ProductVariants.updateVariantsEmptyState;
        ProductVariants.updateVariantsEmptyState = function() {
            // Call the original function
            originalUpdateVariantsEmptyState.call(this);

            // Update the variant count
            const variantCount = $('#variants-table tbody tr').length;
            $('#variant-count').text(variantCount);

            if (variantCount > 0) {
                $('#variant-count-summary').show();
            } else {
                $('#variant-count-summary').hide();
            }
        };

        // Add a class to highlight existing variants on page load
        setTimeout(function() {
            $('#variants-table tbody tr').addClass('existing-variant');
        }, 500);
    });
</script>
<?php /**PATH C:\laragon\www\jualinn\resources\views/data-master/product/scripts/product-variants-improvements.blade.php ENDPATH**/ ?>