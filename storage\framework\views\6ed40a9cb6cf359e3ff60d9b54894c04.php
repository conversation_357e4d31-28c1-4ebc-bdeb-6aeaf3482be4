<div class="tab-pane fade" id="shipping" role="tabpanel">
    <div class="row g-3">
        <div class="col-md-5">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-transparent border-bottom-0">
                    <h6 class="mb-0 fw-semibold"><i class='bx bx-package me-1'></i> Dimensi & Berat</h6>
                </div>
                <div class="card-body pt-0">
                    <div class="mb-3">
                        <label class="form-label small text-muted" for="weight">Berat (gram)</label>
                        <input type="number" class="form-control form-control-sm <?php $__errorArgs = ['weight'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               id="weight" name="weight" value="<?php echo e(old('weight', isset($product) ? $product->weight : '')); ?>" min="0" step="0.01" placeholder="Masukkan berat dalam gram" required>
                        <?php $__errorArgs = ['weight'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="row g-2">
                        <div class="col-md-4">
                            <label class="form-label small text-muted" for="length">Panjang (cm)</label>
                            <input type="number" class="form-control form-control-sm <?php $__errorArgs = ['length'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="length" name="length" value="<?php echo e(old('length', isset($product) ? $product->length : '')); ?>" min="0" step="0.01" placeholder="P" required>
                            <?php $__errorArgs = ['length'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label small text-muted" for="width">Lebar (cm)</label>
                            <input type="number" class="form-control form-control-sm <?php $__errorArgs = ['width'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="width" name="width" value="<?php echo e(old('width', isset($product) ? $product->width : '')); ?>" min="0" step="0.01" placeholder="L" required>
                            <?php $__errorArgs = ['width'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label small text-muted" for="height">Tinggi (cm)</label>
                            <input type="number" class="form-control form-control-sm <?php $__errorArgs = ['height'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="height" name="height" value="<?php echo e(old('height', isset($product) ? $product->height : '')); ?>" min="0" step="0.01" placeholder="T" required>
                            <?php $__errorArgs = ['height'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-7">
            <div class="row g-3">
                <div class="col-md-12">
                    <div class="card shadow-sm border-0 <?php $__errorArgs = ['shipping_methods'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border border-danger <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                        <div class="card-header bg-transparent border-bottom-0">
                            <h6 class="mb-0 fw-semibold"><i class='bx bx-car me-1'></i> Metode Pengiriman <span class="text-danger">*</span></h6>
                        </div>
                        <div class="card-body pt-0">
                            <div class="row">
                                <?php $__currentLoopData = $shippingMethods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $method): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-md-6 mb-2">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input <?php $__errorArgs = ['shipping_methods'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   id="shipping_method_<?php echo e($method->id); ?>"
                                                   name="shipping_methods[]"
                                                   value="<?php echo e($method->id); ?>"
                                                   <?php echo e(in_array($method->id, old('shipping_methods', isset($product) ? $product->shippingMethods->pluck('id')->toArray() : [])) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="shipping_method_<?php echo e($method->id); ?>">
                                                <?php echo e($method->name); ?>

                                            </label>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <?php $__errorArgs = ['shipping_methods'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="text-danger mt-2 small">
                                    <i class='bx bx-error-circle me-1'></i><?php echo e($message); ?>

                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="card shadow-sm border-0 <?php $__errorArgs = ['payment_methods'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border border-danger <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                        <div class="card-header bg-transparent border-bottom-0">
                            <h6 class="mb-0 fw-semibold"><i class='bx bx-credit-card me-1'></i> Metode Pembayaran <span class="text-danger">*</span></h6>
                        </div>
                        <div class="card-body pt-0">
                            <div class="row">
                                <?php $__currentLoopData = $paymentMethods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $method): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-md-6 mb-2">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input <?php $__errorArgs = ['payment_methods'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   id="payment_method_<?php echo e($method->id); ?>"
                                                   name="payment_methods[]"
                                                   value="<?php echo e($method->id); ?>"
                                                   <?php echo e(in_array($method->id, old('payment_methods', isset($product) ? $product->paymentMethods->pluck('id')->toArray() : [])) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="payment_method_<?php echo e($method->id); ?>">
                                                <?php echo e($method->name); ?>

                                            </label>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <?php $__errorArgs = ['payment_methods'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="text-danger mt-2 small">
                                    <i class='bx bx-error-circle me-1'></i><?php echo e($message); ?>

                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Shipping Origins -->
    <div class="card shadow-sm border-0 mt-3">
        <div class="card-header bg-transparent d-flex justify-content-between align-items-center">
            <h6 class="mb-0 fw-semibold"><i class='bx bx-map-pin me-1'></i> Alamat Pengiriman</h6>
            <div>
                <button type="button" class="btn btn-outline-primary btn-sm me-1" id="fetch-saved-addresses">
                    <i class='bx bx-refresh'></i> Muat Alamat
                </button>
                <button type="button" class="btn btn-primary btn-sm" id="add-shipping-origin">
                    <i class='bx bx-plus'></i> Tambah Alamat Baru
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="alert alert-info mb-3">
                <div class="d-flex">
                    <div class="me-2"><i class='bx bx-info-circle fs-5'></i></div>
                    <div>
                        <strong>Alamat Pengiriman:</strong>
                        <ul class="mb-0 ps-3 mt-1">
                            <li>Pilih salah satu alamat tersimpan untuk pengiriman produk</li>
                            <li>Alamat default akan otomatis dipilih</li>
                            <li>Jika ingin menambah alamat baru, klik tombol "Tambah Alamat Baru"</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Radio button for address selection type -->
            <div class="mb-3 d-none">
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="address_selection_type" id="use_saved_address" value="saved" checked>
                    <label class="form-check-label" for="use_saved_address">Gunakan alamat tersimpan</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="address_selection_type" id="use_new_address" value="new">
                    <label class="form-check-label" for="use_new_address">Buat alamat baru</label>
                </div>
            </div>

            <!-- Saved Addresses Section -->
            <div id="saved-addresses-container" class="row mb-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Alamat Tersimpan</h6>
                    <span id="saved-address-count" class="badge bg-primary">0 alamat</span>
                </div>

                <div id="saved-addresses" class="row g-3">
                    <div class="col-12">
                        <div class="alert alert-warning">
                            <i class='bx bx-loader-circle bx-spin me-2'></i>
                            <span>Memuat alamat tersimpan... Klik tombol "Muat Alamat" jika tidak muncul otomatis.</span>
                        </div>
                    </div>
                </div>

                <!-- Empty state for saved addresses -->
                <div id="no-saved-addresses" class="text-center py-4 border-dashed rounded bg-light" style="display: none;">
                    <div class="mb-2">
                        <i class='bx bx-map' style="font-size: 2.5rem; color: #d9dee3;"></i>
                    </div>
                    <h6 class="text-muted">Belum ada alamat tersimpan</h6>
                    <p class="text-muted small mb-0">Klik tombol "Tambah Alamat Baru" untuk membuat alamat</p>
                </div>

                <!-- Hidden container for the selected address data -->
                <div id="selected-address-data"></div>
            </div>

            <!-- New Address Form (Hidden initially) -->
            <div id="shipping-origins-container" style="display: none;">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Alamat Baru</h6>
                    <button type="button" class="btn btn-outline-secondary btn-sm" id="cancel-new-address">
                        <i class='bx bx-x'></i> Batal
                    </button>
                </div>

                <div class="shipping-origin-row mb-3">
                    <div class="card shadow-sm">
                        <div class="card-header bg-transparent py-2 d-flex justify-content-between align-items-center">
                            <div class="d-flex gap-2 align-items-center">
                                <span class="badge bg-primary">Alamat Pengiriman Baru</span>
                                <input type="text" class="form-control form-control-sm address-label-input"
                                       name="shipping_origins[0][name]" placeholder="Nama/Label alamat"
                                       value="<?php echo e(old('shipping_origins.0.name')); ?>" style="width: 200px;">
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row g-2">
                                <div class="col-md-12 mb-2">
                                    <label class="form-label small text-muted d-flex justify-content-between">
                                        <span>Alamat Lengkap <span class="text-danger">*</span></span>
                                        <span class="text-primary" data-bs-toggle="tooltip" data-bs-placement="top" title="Masukkan alamat lengkap termasuk nama jalan, nomor, RT/RW, dll.">
                                            <i class='bx bx-info-circle'></i>
                                        </span>
                                    </label>
                                    <textarea class="form-control <?php $__errorArgs = ['shipping_origins.0.address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="shipping_origins[0][address]" rows="2" placeholder="Masukkan alamat lengkap"><?php echo e(old('shipping_origins.0.address')); ?></textarea>
                                    <?php $__errorArgs = ['shipping_origins.0.address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="col-md-6 mb-2">
                                    <label class="form-label small text-muted d-flex justify-content-between">
                                        <span>Kota/Kabupaten <span class="text-danger">*</span></span>
                                        <span class="text-primary" data-bs-toggle="tooltip" data-bs-placement="top" title="Masukkan nama kota atau kabupaten">
                                            <i class='bx bx-info-circle'></i>
                                        </span>
                                    </label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['shipping_origins.0.city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="shipping_origins[0][city]" value="<?php echo e(old('shipping_origins.0.city')); ?>" placeholder="Masukkan kota">
                                    <?php $__errorArgs = ['shipping_origins.0.city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="col-md-6 mb-2">
                                    <label class="form-label small text-muted d-flex justify-content-between">
                                        <span>Provinsi</span>
                                        <span class="text-primary" data-bs-toggle="tooltip" data-bs-placement="top" title="Masukkan nama provinsi">
                                            <i class='bx bx-info-circle'></i>
                                        </span>
                                    </label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['shipping_origins.0.state'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="shipping_origins[0][state]" value="<?php echo e(old('shipping_origins.0.state')); ?>" placeholder="Masukkan provinsi">
                                    <?php $__errorArgs = ['shipping_origins.0.state'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="col-md-6 mb-2">
                                    <label class="form-label small text-muted d-flex justify-content-between">
                                        <span>Negara</span>
                                        <span class="text-primary" data-bs-toggle="tooltip" data-bs-placement="top" title="Default: Indonesia">
                                            <i class='bx bx-info-circle'></i>
                                        </span>
                                    </label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['shipping_origins.0.country'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="shipping_origins[0][country]" value="<?php echo e(old('shipping_origins.0.country', 'Indonesia')); ?>" placeholder="Masukkan negara">
                                    <?php $__errorArgs = ['shipping_origins.0.country'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="col-md-6 mb-2">
                                    <label class="form-label small text-muted d-flex justify-content-between">
                                        <span>Kode Pos <span class="text-danger">*</span></span>
                                        <span class="text-primary" data-bs-toggle="tooltip" data-bs-placement="top" title="Masukkan 5 digit kode pos">
                                            <i class='bx bx-info-circle'></i>
                                        </span>
                                    </label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['shipping_origins.0.postal_code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="shipping_origins[0][postal_code]" value="<?php echo e(old('shipping_origins.0.postal_code')); ?>" placeholder="Masukkan kode pos (5 digit)">
                                    <?php $__errorArgs = ['shipping_origins.0.postal_code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="col-md-12 d-flex justify-content-between align-items-center mt-1">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" name="shipping_origins[0][save_as_default]" value="1" checked>
                                        <label class="form-check-label">Simpan sebagai alamat utama</label>
                                        <small class="d-block text-muted">Alamat ini akan disimpan untuk digunakan kembali</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('script'); ?>
    <script>
        // Make shipping addresses available to the JavaScript
        window.shippingAddresses = <?php echo json_encode($shippingAddresses, 15, 512) ?>;

        // Ensure all addresses have is_default property (for backward compatibility)
        if (window.shippingAddresses && window.shippingAddresses.length > 0) {
            window.shippingAddresses = window.shippingAddresses.map(addr => {
                // If the address doesn't have is_default but has is_frequently_used, use that
                if (addr.is_default === undefined && addr.is_frequently_used !== undefined) {
                    addr.is_default = addr.is_frequently_used;
                }
                // If neither exists, set is_default to false
                if (addr.is_default === undefined && addr.is_frequently_used === undefined) {
                    addr.is_default = false;
                }
                return addr;
            });
        }

        console.log("Shipping addresses from controller:", window.shippingAddresses);

        $(document).ready(function() {
            // Variabel untuk tracking state - make globally accessible
            window.useSavedAddress = false;
            window.selectedAddressId = null;
            let useSavedAddress = window.useSavedAddress; // Local reference for compatibility
            let selectedAddressId = window.selectedAddressId;

            // Inisialisasi komponen
            initShippingAddressComponent();

            function initShippingAddressComponent() {
                // Cache DOM elements
                const $form = $('form');
                const $savedAddresses = $('#saved-addresses');

                // Event bindings
                $('#fetch-saved-addresses').on('click', loadSavedAddresses);
                $('#add-shipping-origin').on('click', showNewAddressForm);
                $('#cancel-new-address').on('click', hideNewAddressForm);

                // Delegated events
                $savedAddresses.on('change', '.select-saved-address', handleAddressSelection);
                $savedAddresses.on('change', '.saved-address-default-checkbox', handleDefaultAddressChange);
                $(document).on('change', '.origin-default-checkbox', handleNewAddressDefaultCheckbox);

                // Form submission handler removed to prevent conflicts
                // Functionality moved to main form handler

                // Load initial data
                loadSavedAddresses();
            }

            function loadSavedAddresses() {
                showLoadingState();

                // Use the addresses from the window.shippingAddresses variable
                if (window.shippingAddresses && window.shippingAddresses.length > 0) {
                    renderAddresses({
                        addresses: window.shippingAddresses
                    });
                    return;
                }

                // Show no addresses message if no addresses are available
                showErrorState("Belum ada alamat tersimpan. Silakan tambahkan alamat baru.");
            }

            function renderAddresses(response) {
                console.log("Rendering addresses:", response);
                let html = '';

                if (!response?.addresses?.length || !response.addresses) {
                    console.log("No addresses to render");
                    html = `
                    <div class="col-12">
                        <div class="alert alert-warning">
                            <i class='bx bx-info-circle me-2'></i>
                            <span>${response.message || 'Belum ada alamat tersimpan. Silakan tambahkan alamat baru.'}</span>
                        </div>
                    </div>`;
                } else {
                    console.log("Rendering " + response.addresses.length + " addresses");
                    response.addresses.forEach(address => {
                        if (!address) return;
                        html += createAddressCardHtml(address);
                    });
                }

                $('#saved-addresses').html(html);
                console.log("Address HTML rendered");
            }

            function createAddressCardHtml(address) {
                console.log("Creating address card for:", address);
                return `
                    <div class="col-md-6">
                        <div class="card h-100 saved-address-card" data-address-id="${address.id}">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title address-label mb-0">${address.name}</h6>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input select-saved-address"
                                            id="select-address-${address.id}"
                                            name="use_saved_address"
                                            value="${address.id}">
                                        <label class="form-check-label" for="select-address-${address.id}">Pilih</label>
                                    </div>
                                </div>
                                <p class="card-text small mb-1">${address.address}</p>
                                <p class="card-text small mb-1">${address.city}, ${address.state}</p>
                                <p class="card-text small mb-1">${address.country}, ${address.postal_code}</p>
                                <div class="mt-3">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input saved-address-default-checkbox"
                                            id="default-address-${address.id}"
                                            name="saved_address_is_default"
                                            value="${address.id}" disabled>
                                        <label class="form-check-label" for="default-address-${address.id}">Jadikan alamat utama</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>`;
            }

            function handleAddressSelection(e) {
                const $target = $(e.target);
                const addressId = $target.val();

                // Update state - both local and global
                useSavedAddress = window.useSavedAddress = $target.is(':checked');
                selectedAddressId = window.selectedAddressId = useSavedAddress ? addressId : null;

                // Uncheck other addresses
                $('.select-saved-address').not($target).prop('checked', false);

                // Toggle default checkbox
                const $defaultCheckbox = $(`#default-address-${addressId}`);
                $defaultCheckbox.prop('disabled', !useSavedAddress);

                // Manage hidden fields
                if (useSavedAddress) {
                    // Sembunyikan form alamat baru jika visible
                    $('#shipping-origins-container').slideUp();

                    // Update atau buat hidden field untuk address ID
                    let $hiddenInput = $('#selected-address-id-input');
                    if ($hiddenInput.length === 0) {
                        $('form').append(`<input type="hidden" name="selected_address_id" id="selected-address-id-input">`);
                        $hiddenInput = $('#selected-address-id-input');
                    }
                    $hiddenInput.val(addressId);

                    // Set address selection type to 'saved'
                    $('#use_saved_address').prop('checked', true);

                    // Make sure we have a hidden input for address_selection_type
                    if ($('#address-selection-type-input').length === 0) {
                        $('form').append('<input type="hidden" name="address_selection_type" id="address-selection-type-input" value="saved">');
                    } else {
                        $('#address-selection-type-input').val('saved');
                    }
                } else {
                    $('#selected-address-id-input').remove();
                    $defaultCheckbox.prop('checked', false);
                }
            }

            function handleDefaultAddressChange(e) {
                if ($(e.target).is(':checked')) {
                    $('.saved-address-default-checkbox, .origin-default-checkbox').not(e.target).prop('checked', false);
                    $('#shipping-address-is-default-input').remove();
                    $('form').append(`<input type="hidden" name="shipping_address_is_default" value="1" id="shipping-address-is-default-input">`);
                } else {
                    $('#shipping-address-is-default-input').remove();
                }
            }

            function handleNewAddressDefaultCheckbox(e) {
                if ($(e.target).is(':checked')) {
                    $('.origin-default-checkbox, .saved-address-default-checkbox').not(e.target).prop('checked', false);
                }
            }

            function showNewAddressForm() {
                // Ketika menambah alamat baru, pastikan tidak ada alamat tersimpan yang dipilih
                $('.select-saved-address').prop('checked', false).trigger('change');
                $('#shipping-origins-container').slideDown();

                // Set address selection type to 'new'
                $('#use_new_address').prop('checked', true);

                // Remove any selected_address_id input that might exist
                $('#selected-address-id-input').remove();

                // Make sure we have a hidden input for address_selection_type
                if ($('#address-selection-type-input').length === 0) {
                    $('form').append('<input type="hidden" name="address_selection_type" id="address-selection-type-input" value="new">');
                } else {
                    $('#address-selection-type-input').val('new');
                }
            }

            function hideNewAddressForm() {
                $('#shipping-origins-container').slideUp();

                // If no saved address is selected, default to saved address type
                // but without a specific address selected
                if (!selectedAddressId) {
                    // Set address selection type to 'saved'
                    $('#use_saved_address').prop('checked', true);

                    // Make sure we have a hidden input for address_selection_type
                    if ($('#address-selection-type-input').length === 0) {
                        $('form').append('<input type="hidden" name="address_selection_type" id="address-selection-type-input" value="saved">');
                    } else {
                        $('#address-selection-type-input').val('saved');
                    }
                }
            }

            function showLoadingState() {
                $('#saved-addresses').html(`
                <div class="col-12">
                    <div class="alert alert-warning">
                        <i class='bx bx-loader-circle bx-spin me-2'></i>
                        <span>Memuat alamat tersimpan...</span>
                    </div>
                </div>
            `);
            }

            function showErrorState(message = "Gagal memuat alamat tersimpan. Silakan coba lagi.") {
                $('#saved-addresses').html(`
                <div class="col-12">
                    <div class="alert alert-warning">
                        <i class='bx bx-info-circle me-2'></i>
                        <span>${message}</span>
                    </div>
                </div>
            `);

                // Show the new address form
                $('#shipping-origins-container').show();
            }

            // Handle form submission to ensure the correct address selection type is set
            // Converted to global function for main form handler
            window.handleShippingFormSubmission = function() {
                // Check if we have a selected address
                if (window.selectedAddressId) {
                    // Make sure the address selection type is set to 'saved'
                    $('#use_saved_address').prop('checked', true);

                    // Make sure we have a hidden input for address_selection_type
                    if ($('#address-selection-type-input').length === 0) {
                        $('form').append('<input type="hidden" name="address_selection_type" id="address-selection-type-input" value="saved">');
                    } else {
                        $('#address-selection-type-input').val('saved');
                    }

                    // Make sure the selected_address_id field exists and has the correct value
                    let $hiddenInput = $('#selected-address-id-input');
                    if ($hiddenInput.length === 0) {
                        $('form').append(`<input type="hidden" name="selected_address_id" id="selected-address-id-input" value="${window.selectedAddressId}">`);
                    } else {
                        $hiddenInput.val(window.selectedAddressId);
                    }
                } else if ($('#shipping-origins-container').is(':visible')) {
                    // If the new address form is visible, set the address selection type to 'new'
                    $('#use_new_address').prop('checked', true);

                    // Make sure we have a hidden input for address_selection_type
                    if ($('#address-selection-type-input').length === 0) {
                        $('form').append('<input type="hidden" name="address_selection_type" id="address-selection-type-input" value="new">');
                    } else {
                        $('#address-selection-type-input').val('new');
                    }

                    // Remove any selected_address_id input that might exist
                    $('#selected-address-id-input').remove();
                }

                // Log the form state for debugging
                console.log('Form submission - Address selection type:', $('input[name="address_selection_type"]').val());
                console.log('Form submission - Selected address ID:', $('input[name="selected_address_id"]').val() || 'None');
                console.log('Form submission - New address form visible:', $('#shipping-origins-container').is(':visible'));

                // Continue with form submission
                return true;
            };
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\laragon\www\jualinn\resources\views/data-master/product/partials/shipping.blade.php ENDPATH**/ ?>