<?php
    $menu = config('menu');
    $userRole = \App\Enums\LevelUser::getLabel(Auth::user()->level_user);
?>
<div class="sidebar-wrapper" data-simplebar="true">
    <div class="sidebar-header">
        <div>
            <img src="<?php echo e(asset('assets/images/logo-icon.png')); ?>" class="logo-icon" alt="logo icon">
        </div>
        <div>
            <h4 class="logo-text">Ju<PERSON>nn</h4>
        </div>
        <div class="toggle-icon ms-auto"><i class='bx bx-arrow-to-left'></i>
        </div>
    </div>
    <!--navigation-->
    <ul class="metismenu" id="menu">
        <?php $__currentLoopData = $menu; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if(in_array($userRole, $item['roles'])): ?>
                <li class="<?php if(isset($item['submenu'])): ?> has-submenu <?php endif; ?> <?php if(isset($item['route']) && Route::is($item['route'])): ?> mm-active <?php endif; ?>">
                    <a href="<?php echo e(isset($item['route']) && $item['route'] !== '#' ? route($item['route']) : 'javascript:;'); ?>" class="<?php if(isset($item['submenu'])): ?> has-arrow <?php endif; ?>">
                        <div class="parent-icon">
                            <i class="<?php echo e($item['icon']); ?>"></i>
                        </div>
                        <div class="menu-title"><?php echo e($item['text']); ?></div>
                    </a>
                    <?php if(isset($item['submenu'])): ?>
                        <ul>
                            <?php $__currentLoopData = $item['submenu']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if(in_array($userRole, $subItem['roles'])): ?>
                                    <li class="<?php if(isset($subItem['route']) && Route::is($subItem['route'])): ?> mm-active <?php endif; ?>">
                                        <a href="<?php echo e(isset($subItem['route']) && $subItem['route'] !== '#' ? route($subItem['route']) : 'javascript:void(0)'); ?>">
                                            <?php echo e($subItem['text']); ?>

                                        </a>
                                    </li>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    <?php endif; ?>
                </li>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        
    </ul>
    <!--end navigation-->
</div>
<?php /**PATH C:\laragon\www\jualinn\resources\views/templates/sidebar.blade.php ENDPATH**/ ?>