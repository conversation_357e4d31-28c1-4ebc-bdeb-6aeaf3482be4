<script>
    $(document).ready(function() {
        // Load saved addresses on page load
        loadSavedAddresses();

        // Button to reload addresses
        $('#fetch-saved-addresses').click(function() {
            loadSavedAddresses();
        });

        // Add new address button
        $('#add-shipping-origin').on('click', function() {
            // Clear the selected address
            $('.select-address').prop('checked', false);
            $('.saved-address-card').removeClass('border-primary');
            $('#selected-address-data').empty();

            // Show new address form and set the selection type
            $('#shipping-origins-container').slideDown();
            $('#saved-addresses-container').slideUp();
            $('#use_new_address').prop('checked', true);
        });

        // Cancel new address button
        $('#cancel-new-address').on('click', function() {
            // Hide new address form and clear inputs
            $('#shipping-origins-container').slideUp();
            $('#saved-addresses-container').slideDown();
            $('#use_saved_address').prop('checked', true);

            // Clear new address fields
            $('#shipping-origins-container input[type="text"]').val('');
            $('#shipping-origins-container textarea').val('');

            // Check if we need to select a default address
            if ($('.select-address:checked').length === 0) {
                // Find the default address if available
                const defaultRadio = $('.select-address[data-is-default="1"]');
                if (defaultRadio.length) {
                    defaultRadio.prop('checked', true);
                    selectSavedAddress(defaultRadio);
                }
            }
        });

        function loadSavedAddresses() {
            // Get addresses from the controller-provided data
            // This avoids authentication issues with the API
            console.log("Loading saved addresses...");
            console.log("window.shippingAddresses:", window.shippingAddresses);

            // Make sure we have addresses and they have the correct structure
            let addresses = [];

            if (window.shippingAddresses && window.shippingAddresses.length > 0) {
                // Map addresses to ensure they have the is_default property
                addresses = window.shippingAddresses.map(addr => {
                    // If the address doesn't have is_default but has is_frequently_used, use that
                    if (addr.is_default === undefined && addr.is_frequently_used !== undefined) {
                        addr.is_default = addr.is_frequently_used;
                    }
                    // If neither exists, set is_default to false
                    if (addr.is_default === undefined && addr.is_frequently_used === undefined) {
                        addr.is_default = false;
                    }
                    return addr;
                });
            }

            // If we have addresses from the controller, use them
            if (addresses && addresses.length > 0) {
                console.log("Found addresses:", addresses.length);
                renderAddresses({
                    addresses: addresses
                });
                return;
            }

            console.log("No addresses found in window.shippingAddresses");
            // Show no addresses message if no addresses are available
            showErrorState("Tidak ada alamat tersimpan. Silakan tambahkan alamat baru.");
        }

        function showErrorState(message = "Gagal memuat alamat tersimpan. Silakan coba lagi.") {
            $('#saved-addresses').html(`
                <div class="col-12">
                    <div class="alert alert-warning">
                        <i class='bx bx-info-circle me-2'></i>
                        <span>${message}</span>
                    </div>
                </div>
            `);

            // Show the new address form
            $('#shipping-origins-container').show();
        }

        function showLoadingState() {
            $('#saved-addresses').html(`
                <div class="col-12">
                    <div class="alert alert-warning">
                        <i class='bx bx-loader-circle bx-spin me-2'></i>
                        <span>Memuat alamat tersimpan...</span>
                    </div>
                </div>
            `);
        }


        // Function to render addresses from response data
        // function renderAddresses(response) {
        //     try {
        //         console.log("Response data received:", response); // Log data untuk debugging

        //         // Check if the response has data
        //         const addresses = response.data || response.addresses || [];

        //         console.log("Extracted addresses:", addresses); // Log data yang diproses
        //         console.log("Address count:", addresses.length); // Log jumlah alamat

        //         // Update address count
        //         $('#saved-address-count').text(addresses.length + ' alamat');

        //         if (addresses && addresses.length > 0) {
        //             // Hide no addresses message
        //             $('#no-saved-addresses').hide();

        //             // Render addresses
        //             let addressesHtml = '';
        //             let hasDefault = false;
        //             let defaultAddressId = null;

        //             addresses.forEach(function(address) {
        //                 if (address.is_default) {
        //                     hasDefault = true;
        //                     defaultAddressId = address.id;
        //                 }

        //                 addressesHtml += renderSavedAddress(address);
        //             });

        //             $('#saved-addresses').html(addressesHtml);
        //             console.log("Addresses HTML generated:", addressesHtml.length > 0); // Cek apakah HTML dibuat

        //             // Auto-select default address
        //             if (hasDefault) {
        //                 const defaultRadio = $(`.select-address[value="${defaultAddressId}"]`);
        //                 defaultRadio.prop('checked', true);
        //                 selectSavedAddress(defaultRadio);
        //             } else if (addresses.length > 0) {
        //                 // If no default, select the first address
        //                 const firstRadio = $('.select-address').first();
        //                 firstRadio.prop('checked', true);
        //                 selectSavedAddress(firstRadio);
        //             }

        //             // Hide new address form since we have saved addresses
        //             $('#shipping-origins-container').hide();
        //             $('#saved-addresses-container').show();
        //             $('#use_saved_address').prop('checked', true);
        //         } else {
        //             console.log("No addresses found or empty array"); // Log jika tidak ada alamat

        //             // Show no addresses message
        //             $('#saved-addresses').empty();
        //             $('#no-saved-addresses').show();

        //             // Show new address form since we don't have any saved addresses
        //             $('#shipping-origins-container').show();
        //             $('#saved-addresses-container').show();
        //             $('#use_new_address').prop('checked', true);
        //         }
        //     } catch (e) {
        //         console.error("Error parsing address data:", e);
        //         $('#saved-addresses').html(`
        //             <div class="col-12">
        //                 <div class="alert alert-danger">
        //                     <i class='bx bx-error-circle me-2'></i>
        //                     <span>Gagal memproses data alamat. Error: ${e.message}</span>
        //                 </div>
        //             </div>
        //         `);

        //         // Show the new address form as fallback
        //         $('#shipping-origins-container').show();
        //         $('#no-saved-addresses').hide();
        //     }
        // }

        // function renderSavedAddress(address) {
        //     // Format the address for display
        //     const addressLines = [];
        //     if (address.address) addressLines.push(address.address);
        //     if (address.city) {
        //         let cityLine = address.city;
        //         if (address.state) cityLine += `, ${address.state}`;
        //         if (address.postal_code) cityLine += ` ${address.postal_code}`;
        //         addressLines.push(cityLine);
        //     }
        //     if (address.country) addressLines.push(address.country);

        //     // Get address label
        //     const addressLabel = address.name || 'Alamat';

        //     return `
        //         <div class="col-md-6">
        //             <div class="card saved-address-card ${address.is_default ? 'border-primary' : ''}" data-address-id="${address.id}">
        //                 <div class="card-body">
        //                     <div class="d-flex justify-content-between align-items-start mb-2">
        //                         <div class="form-check">
        //                             <input class="form-check-input select-address" type="radio" name="selected_address_id"
        //                                    id="address_${address.id}" value="${address.id}" data-is-default="${address.is_default ? '1' : '0'}">
        //                             <label class="form-check-label fw-semibold" for="address_${address.id}">
        //                                 ${addressLabel}
        //                             </label>
        //                         </div>
        //                         ${address.is_default ? '<span class="badge bg-primary">Default</span>' : ''}
        //                     </div>
        //                     <div class="address-content ms-4">
        //                         ${addressLines.map(line => `<p class="small mb-1">${line}</p>`).join('')}
        //                     </div>
        //                 </div>
        //             </div>
        //         </div>
        //     `;
        // }

        // Improved renderAddresses function
        function renderAddresses(response) {
            try {
                console.log("Response data received:", response); // Debug log

                // Safely extract addresses from response
                const addresses = Array.isArray(response) ? response :
                    (response.data || response.addresses || []);

                console.log("Processed addresses:", addresses); // Debug log

                // Update address count
                $('#saved-address-count').text(addresses.length + ' alamat');

                const $savedAddressesContainer = $('#saved-addresses');
                const $noAddressesMessage = $('#no-saved-addresses');
                const $shippingOriginsContainer = $('#shipping-origins-container');

                if (addresses.length > 0) {
                    // Hide no addresses message
                    $noAddressesMessage.hide();

                    // Clear existing content
                    $savedAddressesContainer.empty();

                    // Render each address
                    let defaultAddressId = null;
                    let addressesHtml = '';

                    addresses.forEach(address => {
                        addressesHtml += renderSavedAddress(address);

                        // Track default address (use is_default)
                        if (address.is_default) {
                            defaultAddressId = address.id;
                        }
                    });

                    // Add the HTML to the container
                    $savedAddressesContainer.html(addressesHtml);

                    // Auto-select address (default or first)
                    if (defaultAddressId) {
                        $(`#address_${defaultAddressId}`).prop('checked', true).trigger('change');
                    } else if (addresses.length > 0) {
                        $('.select-address').first().prop('checked', true).trigger('change');
                    }

                    // Show saved addresses container
                    $('#saved-addresses-container').show();
                    $shippingOriginsContainer.hide();
                    $('#use_saved_address').prop('checked', true);
                } else {
                    console.log("No addresses found"); // Debug log

                    // Show no addresses message
                    $savedAddressesContainer.html(`
                        <div class="col-12">
                            <div class="alert alert-warning">
                                <i class='bx bx-info-circle me-2'></i>
                                <span>Belum ada alamat tersimpan. Silakan tambahkan alamat baru.</span>
                            </div>
                        </div>
                    `);
                    $noAddressesMessage.show();

                    // Show new address form
                    $shippingOriginsContainer.show();
                    $('#use_new_address').prop('checked', true);
                }
            } catch (e) {
                console.error("Error rendering addresses:", e);
                $('#saved-addresses').html(`
                    <div class="col-12">
                        <div class="alert alert-danger">
                            <i class='bx bx-error-circle me-2'></i>
                            <span>Gagal memproses data alamat. Error: ${e.message}</span>
                        </div>
                    </div>
                `);

                // Fallback to showing new address form
                $('#shipping-origins-container').show();
                $('#no-saved-addresses').hide();
            }
        }

        // Improved renderSavedAddress function
        function renderSavedAddress(address) {
            // Validate and sanitize address data
            const safeAddress = {
                id: address.id || '',
                name: address.name || 'Alamat',
                address: address.address || '',
                city: address.city || '',
                state: address.state || '',
                postal_code: address.postal_code || '',
                country: address.country || '',
                // Use is_default field
                is_default: Boolean(address.is_default)
            };

            // Build address lines
            const addressLines = [];
            if (safeAddress.address) addressLines.push(safeAddress.address);

            const cityLine = [
                safeAddress.city,
                safeAddress.state,
                safeAddress.postal_code
            ].filter(Boolean).join(', ');

            if (cityLine) addressLines.push(cityLine);
            if (safeAddress.country) addressLines.push(safeAddress.country);

            // Escape HTML for security
            const escapeHtml = (unsafe) => {
                return unsafe.toString()
                    .replace(/&/g, "&amp;")
                    .replace(/</g, "&lt;")
                    .replace(/>/g, "&gt;")
                    .replace(/"/g, "&quot;")
                    .replace(/'/g, "&#039;");
            };

            return `
                <div class="col-md-6 mb-3">
                    <div class="card saved-address-card ${safeAddress.is_default ? 'border-primary' : ''}"
                         data-address-id="${escapeHtml(safeAddress.id)}">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div class="form-check">
                                    <input class="form-check-input select-address" type="radio"
                                           name="selected_address_id" id="address_${escapeHtml(safeAddress.id)}"
                                           value="${escapeHtml(safeAddress.id)}"
                                           data-is-default="${safeAddress.is_default ? '1' : '0'}">
                                    <label class="form-check-label fw-semibold" for="address_${escapeHtml(safeAddress.id)}">
                                        ${escapeHtml(safeAddress.name)}
                                    </label>
                                </div>
                                ${safeAddress.is_default ? '<span class="badge bg-primary">Default</span>' : ''}
                            </div>
                            <div class="address-content ms-4">
                                ${addressLines.map(line => `<p class="small mb-1">${escapeHtml(line)}</p>`).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Add event handler for select address radio buttons
        $(document).on('change', '.select-address', function() {
            selectSavedAddress($(this));
        });

        // Add event handler for clicking on address cards
        $(document).on('click', '.saved-address-card', function(e) {
            // Only trigger if the click wasn't on the radio button itself (that's handled separately)
            if (!$(e.target).hasClass('select-address') && !$(e.target).hasClass('form-check-label')) {
                const radio = $(this).find('.select-address');
                radio.prop('checked', true);
                selectSavedAddress(radio);
            }
        });

        function selectSavedAddress(radioElement) {
            // Update visual selection
            $('.saved-address-card').removeClass('border-primary');
            radioElement.closest('.saved-address-card').addClass('border-primary');

            // Get address data
            const addressId = radioElement.val();
            const addressCard = radioElement.closest('.saved-address-card');

            // Create hidden inputs with the selected address data
            const addressData = {
                id: addressId,
                name: addressCard.find('.form-check-label').text().trim(),
                address: addressCard.find('.address-content p:first-child').text().trim()
            };

            // Clear previous data and add new hidden inputs
            $('#selected-address-data').empty();
            $('#selected-address-data').append(`
                <input type="hidden" name="selected_address_id" value="${addressData.id}">
            `);

            // Set the address selection type to 'saved'
            $('input[name="address_selection_type"]').val('saved');
            $('#use_saved_address').prop('checked', true);

            // Hide new address form
            $('#shipping-origins-container').hide();

            // Show a confirmation message
            const addressName = addressData.name;
            const confirmationMsg = `<div class="alert alert-success mt-2">
                <i class='bx bx-check-circle me-1'></i> Alamat <strong>${addressName}</strong> dipilih
            </div>`;

            // Add confirmation message if it doesn't exist
            if ($('#address-selection-confirmation').length === 0) {
                $('#selected-address-data').after(`<div id="address-selection-confirmation">${confirmationMsg}</div>`);
            } else {
                $('#address-selection-confirmation').html(confirmationMsg);
            }
        }
    });
</script>
<?php /**PATH C:\laragon\www\jualinn\resources\views/data-master/product/scripts/product-shipping.blade.php ENDPATH**/ ?>