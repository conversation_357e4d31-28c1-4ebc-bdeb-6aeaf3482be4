<script>
    // Product Attributes and Variations Handling
    let selectedAttributes = [];
    let attributeValues = {};

    $(document).ready(function() {
        // Handle attribute selection
        $('#attribute-main-select').on('change', function() {
            const attributeId = $(this).val();
            if (!attributeId) return;

            const attributeName = $(this).find('option:selected').data('name');

            // Check if attribute is already selected
            if (selectedAttributes.includes(attributeName)) {
                Swal.fire({
                    title: "Perhatian!",
                    text: `Atribut "${attributeName}" sudah dipilih`,
                    icon: "warning",
                    confirmButtonText: "OK",
                    confirmButtonColor: "#696cff"
                });
                $(this).val('');
                return;
            }

            // Handle custom attribute creation
            if (attributeId === 'custom') {
                Swal.fire({
                    title: 'Buat Atribut Baru',
                    input: 'text',
                    inputLabel: 'Nama Atribut',
                    inputPlaceholder: 'Contoh: Ukuran, Warna, dll.',
                    showCancelButton: true,
                    confirmButtonText: 'Buat',
                    cancelButtonText: 'Batal',
                    inputValidator: (value) => {
                        if (!value) {
                            return 'Nama atribut harus diisi!';
                        }
                        if (selectedAttributes.includes(value)) {
                            return `Atribut "${value}" sudah ada!`;
                        }
                    }
                }).then((result) => {
                    if (result.isConfirmed) {
                        const newAttributeName = result.value;

                        // Add new attribute to list
                        selectedAttributes.push(newAttributeName);
                        attributeValues[newAttributeName] = [];

                        // Add attribute card
                        addAttributeCard(newAttributeName);

                        // Reset select
                        $(this).val('');
                    } else {
                        $(this).val('');
                    }
                });
            } else {
                // Add existing attribute
                selectedAttributes.push(attributeName);
                attributeValues[attributeName] = [];

                // Add attribute card
                addAttributeCard(attributeName);

                // Reset select
                $(this).val('');
            }
        });

        // Use the global function from ProductVariants if available
        if (typeof ProductVariants !== 'undefined') {
            addAttributeCard = ProductVariants.createAttributeCard;
        }

        // Function to add attribute card
        function addAttributeCard(attributeName) {
            const cardId = 'attr-' + attributeName.toLowerCase().replace(/[^a-z0-9]/g, '-');

            const cardHtml = `
            <div class="attribute-card mb-3" id="${cardId}" data-attribute="${attributeName}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">${attributeName}</h6>
                    <button type="button" class="btn btn-sm btn-outline-danger delete-attribute">
                        <i class="bx bx-trash"></i>
                    </button>
                </div>
                <div class="card-body">
                    <p class="text-muted small">Tambahkan nilai untuk atribut "${attributeName}"</p>

                    <div class="attribute-value-pills mb-3">
                        <!-- Values will be added here -->
                    </div>

                    <div class="input-group">
                        <input type="text" class="form-control attribute-value-input" placeholder="Nilai baru...">
                        <button class="btn btn-primary add-attribute-value" type="button">
                            <i class="bx bx-plus"></i> Tambah
                        </button>
                    </div>

                    <input type="hidden" name="variant_attributes[]" value="${attributeName}">
                </div>
            </div>
        `;

            $('#attribute-cards').append(cardHtml);
            $('#empty-attributes-state').hide();

            // Update attribute summary
            if (typeof ProductVariants !== 'undefined') {
                ProductVariants.updateAttributeSummary();
            } else {
                updateAttributeSummary();
            }
        }

        // Use the global function from ProductVariants if available
        window.addValueToAttribute = function(attributeName, value) {
            if (typeof ProductVariants !== 'undefined') {
                return ProductVariants.addValueToAttribute(attributeName, value);
            } else {
                // Fallback implementation
                const card = $(`#attr-${attributeName.toLowerCase().replace(/[^a-z0-9]/g, '-')}`);
                if (!card.length) return false;

                // Check if value already exists
                if (attributeValues[attributeName].includes(value)) return false;

                // Add value to array
                attributeValues[attributeName].push(value);

                // Add value pill
                const pillHtml = `
                <div class="attribute-value-pill" data-value="${value}">
                    ${value}
                    <button type="button" class="btn-close ms-2 delete-attribute-value"></button>
                    <input type="hidden" name="variant_values[${attributeName}][]" value="${value}">
                </div>
                `;

                card.find('.attribute-value-pills').append(pillHtml);

                // Update attribute summary
                updateAttributeSummary();

                return true;
            }
        };

        // Add attribute value
        $(document).on('click', '.add-attribute-value', function() {
            const card = $(this).closest('.attribute-card');
            const attributeName = card.data('attribute');
            const valueInput = card.find('.attribute-value-input');
            const value = valueInput.val().trim();

            if (!value) {
                return;
            }

            valueInput.removeClass('is-invalid');

            // Add value to array
            attributeValues[attributeName].push(value);

            // Add value pill
            const pillHtml = `
            <div class="attribute-value-pill" data-value="${value}">
                ${value}
                <button type="button" class="btn-close ms-2 delete-attribute-value"></button>
                <input type="hidden" name="variant_values[${attributeName}][]" value="${value}">
            </div>
        `;

            card.find('.attribute-value-pills').append(pillHtml);
            valueInput.val('');

            // Update attribute summary
            if (typeof ProductVariants !== 'undefined') {
                ProductVariants.updateAttributeSummary();
            } else {
                updateAttributeSummary();
            }

            // Update hidden inputs
            updateHiddenInputs();
        });

        // Handle enter key on attribute value input
        $(document).on('keypress', '.attribute-value-input', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                $(this).closest('.card-body').find('.add-attribute-value').click();
            }
        });

        // Delete attribute value
        $(document).on('click', '.delete-attribute-value', function() {
            const pill = $(this).closest('.attribute-value-pill');
            const card = pill.closest('.attribute-card');
            const attributeName = card.data('attribute');
            const value = pill.data('value');

            // Remove value from array
            attributeValues[attributeName] = attributeValues[attributeName].filter(v => v !== value);

            // Remove pill
            pill.remove();

            // Update attribute summary
            if (typeof ProductVariants !== 'undefined') {
                ProductVariants.updateAttributeSummary();
            } else {
                updateAttributeSummary();
            }

            // Update hidden inputs
            updateHiddenInputs();
        });

        // Delete attribute
        $(document).on('click', '.delete-attribute', function() {
            const card = $(this).closest('.attribute-card');
            const attributeName = card.data('attribute');

            Swal.fire({
                title: "Hapus Atribut?",
                text: `Anda yakin ingin menghapus atribut "${attributeName}" dan semua nilainya?`,
                icon: "question",
                showCancelButton: true,
                confirmButtonText: "Ya, Hapus",
                cancelButtonText: "Batal",
                confirmButtonColor: "#d33",
                cancelButtonColor: "#3085d6",
            }).then((result) => {
                if (result.isConfirmed) {
                    // Remove attribute from arrays
                    selectedAttributes = selectedAttributes.filter(a => a !== attributeName);
                    delete attributeValues[attributeName];

                    // Remove card
                    card.remove();

                    // Show empty state if no attributes
                    if (selectedAttributes.length === 0) {
                        $('#empty-attributes-state').show();
                        $('#attribute-summary').hide();
                    } else {
                        if (typeof ProductVariants !== 'undefined') {
                            ProductVariants.updateAttributeSummary();
                        } else {
                            updateAttributeSummary();
                        }

                        // Update hidden inputs
                        updateHiddenInputs();
                    }
                }
            });
        });
    });

    // Define a fallback function for updateAttributeSummary
    function updateAttributeSummary() {
        if (selectedAttributes.length === 0) {
            $('#no-attributes-message').show();
            $('#total-combinations').text('0');
            return;
        }

        $('#no-attributes-message').hide();

        let combinationCount = 1;
        let summaryHtml = '';
        let hasValues = false;

        for (const attr of selectedAttributes) {
            const values = attributeValues[attr];
            if (values.length > 0) {
                hasValues = true;
                combinationCount *= values.length;

                summaryHtml += `
                <div class="mb-3">
                    <h6>${attr}</h6>
                    <div class="attribute-value-pills">
                        ${values.map(val => `<div class="attribute-value-pill">${val}</div>`).join('')}
                    </div>
                </div>
            `;
            } else {
                summaryHtml += `
                <div class="mb-3">
                    <h6>${attr}</h6>
                    <div class="alert alert-warning py-1 px-2">
                        <small><i class='bx bx-error-circle me-1'></i>Belum ada nilai untuk atribut ini</small>
                    </div>
                </div>
            `;
            }
        }

        $('#attribute-summary-content').html(summaryHtml);
        $('#total-combinations').text(combinationCount);

        // Disable generate button if no combinations possible
        if (combinationCount === 0 || !hasValues) {
            $('#generate-variants-from-summary').attr('disabled', true);
        } else {
            $('#generate-variants-from-summary').attr('disabled', false);
        }
    }

    // Use the global function from ProductVariants if available
    // This is kept for backward compatibility
    window.updateAttributeSummary = function() {
        if (typeof ProductVariants !== 'undefined') {
            return ProductVariants.updateAttributeSummary();
        } else {
            return updateAttributeSummary();
        }
    }

    // Function to update hidden inputs for variant attributes and values
    function updateHiddenInputs() {
        // Clear existing hidden inputs
        $('#variant-hidden-inputs').empty();

        // If has_variants is checked but no attributes are selected, add a dummy input
        if ($('#has_variants_hidden').val() === '1' && (!selectedAttributes || selectedAttributes.length === 0)) {
            // Add a dummy input to satisfy the validation
            $('#variant-hidden-inputs').append(`<input type="hidden" name="variant_attributes[]" value="">`);
            $('#variant-hidden-inputs').append(`<input type="hidden" name="variant_values[dummy][]" value="">`);
            return;
        }

        // Add hidden inputs for each attribute and its values
        selectedAttributes.forEach(attr => {
            // Add attribute input
            $('#variant-hidden-inputs').append(`<input type="hidden" name="variant_attributes[]" value="${attr}">`);

            // Add value inputs for this attribute
            if (attributeValues[attr] && attributeValues[attr].length > 0) {
                attributeValues[attr].forEach(val => {
                    $('#variant-hidden-inputs').append(`<input type="hidden" name="variant_values[${attr}][]" value="${val}">`);
                });
            } else {
                // Add a dummy value to satisfy the validation
                $('#variant-hidden-inputs').append(`<input type="hidden" name="variant_values[${attr}][]" value="">`);
            }
        });
    }

    // Update hidden inputs when the page loads
    $(document).ready(function() {
        updateHiddenInputs();
    });
</script>
<?php /**PATH C:\laragon\www\jualinn\resources\views/data-master/product/scripts/product-attributes.blade.php ENDPATH**/ ?>