<script>
    $(document).ready(function() {
        // Initialize the select2 for responsible users
        $('#responsible_users').select2({
            theme: "bootstrap-5",
            width: '100%',
            placeholder: "Pi<PERSON>h penanggung jawab",
            closeOnSelect: false,
            allowClear: true,
            multiple: true,
            dropdownParent: $('#productForm')
        });

        // Log the selected values for debugging
        console.log('Responsible users selected values:', $('#responsible_users').val());

        // Force refresh the select2 to ensure it shows the selected values
        $('#responsible_users').trigger('change');

        // Initialize Clipboard.js
        const clipboardSlug = new ClipboardJS('#copy-slug', {
            text: function() {
                // Return the full URL for copying
                const baseUrl = '<?php echo e(config('app.url')); ?>/order/';
                const slug = $('#slug').val();
                return baseUrl + slug;
            }
        });
        const clipboardVariants = new ClipboardJS('#copy-variants');

        // Generate slug from product name
        $('#name').on('keyup change', function() {
            // Only auto-generate if slug is empty or hasn't been manually edited
            if ($('#slug').data('manually-edited') !== true) {
                const productName = $(this).val();
                if (productName) {
                    // Convert to lowercase, replace spaces with hyphens, remove special chars
                    const slug = productName
                        .toLowerCase()
                        .replace(/[^\w\s-]/g, '') // Remove special characters
                        .replace(/\s+/g, '-') // Replace spaces with hyphens
                        .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
                        .trim(); // Trim whitespace

                    const uniqueChar = Math.random().toString(36).substr(2, 5);
                    const uniqueSlug = slug + '-' + uniqueChar;

                    $('#slug').val(uniqueSlug);
                }
            }
        });

        // Mark slug as manually edited when user types in it
        $('#slug').on('keyup', function() {
            $(this).data('manually-edited', true);
        });

        // Handle clipboard success
        clipboardSlug.on('success', function(e) {
            // Get the full URL including the domain
            const baseUrl = '<?php echo e(config('app.url')); ?>/order/';
            const slug = $('#slug').val();
            const fullUrl = baseUrl + slug;

            // Change button appearance temporarily
            const button = $(e.trigger);
            const originalText = button.html();

            // Change button appearance temporarily
            button.html('<i class="bx bx-check"></i> Disalin!');
            button.removeClass('btn-outline-secondary').addClass('btn-success');

            // Show SweetAlert with the copied URL
            Swal.fire({
                title: "URL Berhasil Disalin!",
                html: `
                        <p>URL checkout telah disalin ke clipboard:</p>
                        <div class="input-group mt-2">
                            <input type="text" class="form-control" value="${fullUrl}" readonly>
                        </div>
                    `,
                icon: "success",
                timer: 3000,
                timerProgressBar: true,
                showConfirmButton: false
            });

            // Reset button after a delay
            setTimeout(() => {
                button.html(originalText);
                button.removeClass('btn-success').addClass('btn-outline-secondary');
            }, 2000);

            e.clearSelection();
        });

        clipboardVariants.on('success', function(e) {
            // Get the number of variants
            const variantsText = $('#variants-data').val();
            const variantCount = (variantsText.match(/\n/g) || []).length;

            // Create a tooltip-style notification near the button
            const button = $(e.trigger);
            const originalText = button.html();

            // Change button appearance temporarily
            button.html('<i class="bx bx-check"></i> Disalin!');
            button.removeClass('btn-outline-primary').addClass('btn-success');

            // Show SweetAlert with more detailed information
            Swal.fire({
                title: "Data Variasi Berhasil Disalin!",
                html: `
                        <p>Data variasi produk telah disalin ke clipboard.</p>
                        <div class="alert alert-success mt-2">
                            <i class="bx bx-info-circle me-1"></i>
                            Data yang disalin berisi informasi lengkap tentang variasi produk
                            yang dapat Anda tempel (paste) ke aplikasi lain seperti Excel, Word, atau Notepad.
                        </div>
                        `,
                icon: "success",
                timer: 3000,
                timerProgressBar: true,
                showConfirmButton: false
            });

            // Reset button after a delay
            setTimeout(() => {
                button.html(originalText);
                button.removeClass('btn-success').addClass('btn-outline-primary');
            }, 2000);

            e.clearSelection();
        });

        // Function to ensure empty numeric fields are handled properly
        function processEmptyNumericFields() {
            // Fields that should be null if empty
            const numericFields = ['discount_price', 'cost_price'];

            // Process main product fields
            numericFields.forEach(field => {
                const input = $(`#${field}`);
                if (input.length && input.val() === '') {
                    // Create a hidden input with null value
                    input.after(`<input type="hidden" name="${field}" value="">`);
                    // Disable the original input to prevent it from being submitted
                    input.prop('disabled', true);
                }
            });

            // Process variant fields
            if ($('#has_variants').is(':checked')) {
                $('#variants-table .variant-sale-price').each(function() {
                    if ($(this).val() === '') {
                        const name = $(this).attr('name');
                        $(this).after(`<input type="hidden" name="${name}" value="">`);
                        $(this).prop('disabled', true);
                    }
                });
            }

            // Process wholesale prices
            if ($('#has_wholesale').is(':checked')) {
                $('.wholesale-price').each(function() {
                    if ($(this).val() === '') {
                        const name = $(this).attr('name');
                        $(this).after(`<input type="hidden" name="${name}" value="">`);
                        $(this).prop('disabled', true);
                    }
                });
            }
        }

        // Handle form submission - REMOVED to prevent conflicts
        // This handler has been consolidated into product-form.blade.php
        // to prevent multiple submit handlers causing loops

        // Utility function for processing empty numeric fields
        window.processEmptyNumericFields = processEmptyNumericFields;

        // Shipping address handling function - moved to window for global access
        window.handleShippingAddressSubmission = function() {
            // Check if using saved address (from global variable)
            if (window.useSavedAddress && window.selectedAddressId) {
                // Using saved address - remove shipping origins form data
                $('[name^="shipping_origins"]').remove();

                const isDefault = $('#default-address-' + window.selectedAddressId).is(':checked');

                // Make sure default is properly set
                if (isDefault) {
                    $('input[name="saved_address[is_default]"]').val('1');
                } else {
                    $('input[name="saved_address[is_default]"]').val('0');
                }
            } else {
                // Using new address - remove saved address ID
                $('#selected-address-id-input').remove();
            }
        };
    });
</script>
<?php /**PATH C:\laragon\www\jualinn\resources\views/data-master/product/scripts/product-utilities.blade.php ENDPATH**/ ?>