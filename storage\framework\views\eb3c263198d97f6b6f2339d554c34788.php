<script>
    // Utility functions for Indonesian currency format
    function parseIndonesianNumber(formattedNumber) {
        if (!formattedNumber) return 0;
        // Remove all dots (thousand separators) and replace comma with dot
        return parseFloat(formattedNumber.replace(/\./g, '').replace(',', '.')) || 0;
    }

    function formatNumberToIndonesian(number) {
        // Split into integer and decimal parts
        const parts = number.toString().split('.');
        const integerPart = parts[0];
        const decimalPart = parts.length > 1 ? parts[1] : '';

        // Add thousands separator to integer part
        const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, '.');

        // Return formatted number (with decimal if exists)
        return decimalPart ? formattedInteger + ',' + decimalPart : formattedInteger;
    }

    $(document).ready(function() {
        // Update empty states initially
        if (typeof ProductVariants !== 'undefined') {
            ProductVariants.updateVariantsEmptyState();
            ProductVariants.updateAttributeSummary();
        }

        // Validate variant prices on page load
        setTimeout(function() {
            validateVariantPrices();
        }, 500);

        // Generate variants based on attributes
        $('#generate-variants-from-summary').on('click', function() {
            console.log('Generate variants button clicked');

            // Check if we have any attributes
            if (!selectedAttributes || selectedAttributes.length === 0) {
                Swal.fire({
                    title: "Atribut Diperlukan",
                    text: "Silakan tambahkan minimal satu atribut dan nilai pada langkah 1 terlebih dahulu.",
                    icon: "warning",
                });
                return;
            }

            console.log('Selected attributes:', selectedAttributes);
            console.log('Attribute values:', attributeValues);

            // Validate attribute values
            let hasEmptyValues = false;
            for (const attr of selectedAttributes) {
                if (!attributeValues[attr] || attributeValues[attr].length === 0) {
                    hasEmptyValues = true;
                    break;
                }
            }

            if (hasEmptyValues) {
                Swal.fire({
                    title: "Nilai Atribut Diperlukan",
                    text: "Pastikan setiap atribut memiliki minimal satu nilai.",
                    icon: "warning",
                });
                return;
            }

            // Generate all combinations
            const combinations = generateCombinations(attributeValues);
            console.log('Generated combinations:', combinations);

            if (combinations.length === 0) {
                Swal.fire({
                    title: "Tidak Ada Kombinasi",
                    text: "Pastikan setiap atribut memiliki minimal satu nilai.",
                    icon: "warning",
                });
                return;
            }

            // Clear existing variants
            $('#variants-table tbody').empty();

            try {
                // Add variants to table
                combinations.forEach((combination, index) => {
                    console.log(`Adding combination ${index + 1}:`, combination);
                    addVariantRow(combination);
                });

                // Update empty state
                if (typeof ProductVariants !== 'undefined') {
                    ProductVariants.updateVariantsEmptyState();
                } else {
                    console.error('ProductVariants object is not defined');
                }

                // Show SweetAlert with success message
                Swal.fire({
                    title: "Variasi Berhasil Dibuat!",
                    text: `${combinations.length} variasi produk telah dibuat berdasarkan kombinasi atribut.`,
                    icon: "success",
                    timer: 3000,
                    timerProgressBar: true,
                    showConfirmButton: false
                });
            } catch (error) {
                console.error('Error generating variants:', error);
                Swal.fire({
                    title: "Terjadi Kesalahan",
                    text: "Gagal membuat variasi produk. Silakan coba lagi atau muat ulang halaman.",
                    icon: "error"
                });
            }
        });

        // Helper function to generate all combinations of attributes
        function generateCombinations(attributeValues) {
            console.log('Generating combinations from:', attributeValues);

            // Validate input
            if (!attributeValues || typeof attributeValues !== 'object') {
                console.error('Invalid attributeValues:', attributeValues);
                return [];
            }

            const attrs = Object.keys(attributeValues);

            // Check if we have any attributes
            if (attrs.length === 0) {
                console.warn('No attributes found in attributeValues');
                return [];
            }

            const result = [];

            function generateHelper(attrIndex, current) {
                if (attrIndex === attrs.length) {
                    // Make a deep copy of the current combination
                    result.push({
                        ...current
                    });
                    return;
                }

                const attr = attrs[attrIndex];
                const values = attributeValues[attr];

                // Check if values is an array and not empty
                if (!Array.isArray(values) || values.length === 0) {
                    console.warn(`No values for attribute "${attr}"`);
                    return;
                }

                for (const val of values) {
                    // Create a new object for each branch to avoid reference issues
                    const newCurrent = {
                        ...current
                    };
                    newCurrent[attr] = val;
                    generateHelper(attrIndex + 1, newCurrent);
                }
            }

            generateHelper(0, {});
            console.log('Generated combinations:', result);
            return result;
        }

        // Use the global function from ProductVariants
        window.addVariantRowWithData = function(data) {
            return ProductVariants.addVariantRowWithData(data);
        };

        // Add variant row to table
        function addVariantRow(combination) {
            // Ensure ProductVariants object is available
            if (typeof ProductVariants === 'undefined' || typeof ProductVariants.addVariantRowWithData !== 'function') {
                console.error('ProductVariants object or addVariantRowWithData function is not available');
                Swal.fire({
                    title: "Error",
                    text: "Terjadi kesalahan saat membuat variasi. Silakan muat ulang halaman dan coba lagi.",
                    icon: "error"
                });
                return;
            }

            // Get price values, ensuring they're valid numbers
            const regularPrice = $('#regular_price').val() ? parseFloat($('#regular_price').val()) : 0;
            const salePrice = $('#discount_price').val() ? parseFloat($('#discount_price').val()) : '';

            console.log('Adding variant with combination:', combination);
            console.log('Using price:', regularPrice);
            console.log('Using sale price:', salePrice);

            // Call the global function with proper data
            ProductVariants.addVariantRowWithData({
                combination: combination,
                price: regularPrice,
                discount_price: salePrice
            });
        }

        // Delete variant
        $(document).on('click', '.delete-variant', function() {
            const row = $(this).closest('tr');

            Swal.fire({
                title: "Hapus Variasi?",
                text: "Anda yakin ingin menghapus variasi ini?",
                icon: "question",
                showCancelButton: true,
                confirmButtonText: "Ya, Hapus",
                cancelButtonText: "Batal",
                confirmButtonColor: "#d33",
                cancelButtonColor: "#3085d6",
            }).then((result) => {
                if (result.isConfirmed) {
                    row.remove();

                    // Renumber remaining rows
                    $('#variants-table tbody tr').each(function(index) {
                        $(this).find('input[name^="variants["]').each(function() {
                            const name = $(this).attr('name');
                            $(this).attr('name', name.replace(/variants\[\d+\]/, `variants[${index}]`));
                        });
                    });

                    // Update empty state
                    ProductVariants.updateVariantsEmptyState();
                }
            });
        });

        // Use the global function from ProductVariants
        window.updateVariantsEmptyState = function() {
            return ProductVariants.updateVariantsEmptyState();
        }

        // Use the global function from ProductVariants
        window.updateAttributeSummary = function() {
            return ProductVariants.updateAttributeSummary();
        }

        // Mass update variants
        $('#mass-update-variants').on('click', function() {
            // Show the mass update modal
            const massUpdateModal = new bootstrap.Modal(document.getElementById('massUpdateModal'));
            massUpdateModal.show();
        });

        // Apply mass update
        $('#apply-mass-update').on('click', function() {
            const massPriceFormatted = $('#mass-price').val();
            const massSalePriceFormatted = $('#mass-discount-price').val();
            const generateCodes = $('#generate-variant-codes').is(':checked');

            let updateCount = 0;

            // Apply changes to all variant rows
            $('#variants-table tbody tr').each(function(index) {
                // Update price if provided
                if (massPriceFormatted !== '') {
                    // Format the price with Indonesian format
                    $(this).find('.variant-price').val(massPriceFormatted);
                    updateCount++;
                }

                // Update sale price if provided
                if (massSalePriceFormatted !== '') {
                    // Format the sale price with Indonesian format
                    $(this).find('.variant-sale-price').val(massSalePriceFormatted);
                    updateCount++;
                }

                // Generate code if checked
                if (generateCodes) {
                    // Get combination text from the first cell
                    const combination = $(this).find('td:first-child').text().trim();

                    // Create a code from the combination and index
                    const code = 'VAR-' + combination.substring(0, 3).toUpperCase().replace(/\W+/g, '') + '-' + (index + 1);

                    $(this).find('.variant-code').val(code);
                    updateCount++;
                }
            });

            // Close the modal
            const massUpdateModal = bootstrap.Modal.getInstance(document.getElementById('massUpdateModal'));
            massUpdateModal.hide();

            // Show success message
            if (updateCount > 0) {
                Swal.fire({
                    title: "Variasi Berhasil Diupdate!",
                    text: "Data variasi produk telah diperbarui.",
                    icon: "success",
                    timer: 2000,
                    timerProgressBar: true,
                    showConfirmButton: false
                });

                // Clear the inputs for next use
                $('#mass-price').val('');
                $('#mass-discount-price').val('');
                $('#generate-variant-codes').prop('checked', false);
            } else {
                Swal.fire({
                    title: "Tidak ada perubahan",
                    text: "Pilih minimal satu opsi untuk mengupdate variasi.",
                    icon: "info",
                    timer: 2000,
                    timerProgressBar: true,
                    showConfirmButton: false
                });
            }
        });

        // Copy variants to clipboard
        $('#copy-variants').on('click', function() {
            let variantsText = '';

            $('#variants-table tbody tr').each(function() {
                const combination = $(this).find('td:first-child').text().trim();
                const code = $(this).find('.variant-code').val() || 'Auto';
                const price = $(this).find('.variant-price').val() || '0';
                const salePrice = $(this).find('.variant-sale-price').val() || '-';

                variantsText += `${combination}\t${code}\tRp ${price}\tRp ${salePrice}\n`;
            });

            $('#variants-data').val(variantsText);
        });

        // Copy product data to variants
        $('#copy-product-data').on('click', function() {
            const regularPrice = $('#regular_price').val() || '';
            const salePrice = $('#discount_price').val() || '';

            $('#variants-table tbody tr').each(function() {
                $(this).find('.variant-price').val(regularPrice);
                $(this).find('.variant-sale-price').val(salePrice);
            });

            Swal.fire({
                title: "Data Berhasil Disalin!",
                text: "Harga reguler dan harga promo produk telah disalin ke semua variasi.",
                icon: "success",
                timer: 2000,
                timerProgressBar: true,
                showConfirmButton: false
            });
        });

        // Validate variant prices when they change
        $(document).on('change', '.variant-price', function() {
            validateVariantPrices();
        });

        // Validate variant prices when regular price changes
        $('#regular_price').on('change', function() {
            validateVariantPrices();
        });

        // Function to validate variant prices
        function validateVariantPrices() {
            // Only run validation if we have variants
            if ($('#has_variants_hidden').val() !== '1' || $('#variants-table tbody tr').length === 0) {
                return;
            }

            // Parse regular price with Indonesian format
            const regularPriceFormatted = $('#regular_price').val();
            const regularPrice = parseIndonesianNumber(regularPriceFormatted);

            let allSamePrice = true;
            let firstPrice = null;
            let minPrice = Infinity;
            let maxPrice = -Infinity;

            // Check all variant prices
            $('#variants-table tbody tr').each(function() {
                const variantPriceFormatted = $(this).find('.variant-price').val();
                const variantPrice = parseIndonesianNumber(variantPriceFormatted);

                // Set first price if not set
                if (firstPrice === null) {
                    firstPrice = variantPrice;
                } else if (variantPrice !== firstPrice) {
                    allSamePrice = false;
                }

                // Update min and max prices
                if (variantPrice < minPrice) minPrice = variantPrice;
                if (variantPrice > maxPrice) maxPrice = variantPrice;
            });

            // If all variant prices are the same but different from regular price
            if (allSamePrice && firstPrice !== regularPrice && firstPrice !== null) {
                // Show warning
                $('#price-mismatch-warning').remove(); // Remove any existing warning

                const warningHtml = `
                    <div id="price-mismatch-warning" class="alert alert-warning mt-2">
                        <i class="fas fa-exclamation-triangle"></i>
                        Harga reguler (Rp ${regularPrice.toLocaleString('id-ID')}) berbeda dengan harga varian (Rp ${firstPrice.toLocaleString('id-ID')}).
                        <button type="button" class="btn btn-sm btn-warning ms-2" id="sync-regular-price">
                            Sesuaikan Harga Reguler
                        </button>
                        <button type="button" class="btn btn-sm btn-warning ms-2" id="sync-variant-prices">
                            Sesuaikan Harga Varian
                        </button>
                    </div>
                `;

                $('#regular_price').closest('.col-md-6').append(warningHtml);
            }
            // If variant prices are different, show price range warning
            else if (!allSamePrice && minPrice !== Infinity && maxPrice !== -Infinity) {
                $('#price-mismatch-warning').remove(); // Remove any existing warning

                const warningHtml = `
                    <div id="price-mismatch-warning" class="alert alert-info mt-2">
                        <i class="fas fa-info-circle"></i>
                        Produk ini memiliki rentang harga dari Rp ${minPrice.toLocaleString('id-ID')} hingga Rp ${maxPrice.toLocaleString('id-ID')}.
                    </div>
                `;

                $('#regular_price').closest('.col-md-6').append(warningHtml);
            } else {
                // Remove warning if prices match
                $('#price-mismatch-warning').remove();
            }
        }

        // Sync regular price with variant prices
        $(document).on('click', '#sync-regular-price', function() {
            const variantPriceFormatted = $('#variants-table tbody tr:first').find('.variant-price').val();
            const firstPrice = parseIndonesianNumber(variantPriceFormatted);
            $('#regular_price').val(formatNumberToIndonesian(firstPrice));
            $('#price-mismatch-warning').remove();

            Swal.fire({
                title: "Harga Berhasil Disesuaikan!",
                text: "Harga reguler telah disesuaikan dengan harga varian.",
                icon: "success",
                timer: 2000,
                timerProgressBar: true,
                showConfirmButton: false
            });
        });

        // Sync variant prices with regular price
        $(document).on('click', '#sync-variant-prices', function() {
            const regularPriceFormatted = $('#regular_price').val();
            const regularPrice = parseIndonesianNumber(regularPriceFormatted);
            const formattedPrice = formatNumberToIndonesian(regularPrice);

            $('#variants-table tbody tr').each(function() {
                $(this).find('.variant-price').val(formattedPrice);
            });

            $('#price-mismatch-warning').remove();

            Swal.fire({
                title: "Harga Berhasil Disesuaikan!",
                text: "Harga varian telah disesuaikan dengan harga reguler.",
                icon: "success",
                timer: 2000,
                timerProgressBar: true,
                showConfirmButton: false
            });
        });
    });
</script>
<?php /**PATH C:\laragon\www\jualinn\resources\views/data-master/product/scripts/product-variants.blade.php ENDPATH**/ ?>