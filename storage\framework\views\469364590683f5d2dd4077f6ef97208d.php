<?php $__env->startSection('content'); ?>
<div class="row row-cols-1 row-cols-md-2 row-cols-lg-2 row-cols-xl-4">
    <div class="col">
        <div class="card radius-10">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div>
                        <p class="mb-0 text-secondary">Pendapatan</p>
                        <h4 class="my-1">Rp.<?php echo e(number_format($data['pendapatan'], 0, ',', '.')); ?></h4>
                        <?php
                            $pendapatanMingguLalu = $data['pendapatan_minggu_lalu'] > 0
                                ? (($data['pendapatan'] - $data['pendapatan_minggu_lalu']) / $data['pendapatan_minggu_lalu']) * 100
                                : 0;
                        ?>
                        <?php if($pendapatanMingguLalu > 0): ?>
                            <p class="mb-0 font-13 text-success"><i class="bx bxs-up-arrow align-middle"></i><?php echo e(number_format($pendapatanMingguLalu, 2)); ?>% from last week</p>
                        <?php else: ?>
                            <p class="mb-0 font-13 text-danger"><i class="bx bxs-down-arrow align-middle"></i><?php echo e(number_format(abs($pendapatanMingguLalu), 2)); ?>% from last week</p>
                        <?php endif; ?>
                    </div>
                    <div class="widgets-icons rounded-circle text-white ms-auto bg-gradient-burning"><i class="bx bxs-wallet"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card radius-10">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div>
                        <p class="mb-0 text-secondary">Total Order</p>
                        <h4 class="my-1"><?php echo e($data['order']); ?></h4>
                        <?php
                            $orderMingguLalu = $data['order_minggu_lalu'] > 0
                                ? (($data['order'] - $data['order_minggu_lalu']) / $data['order_minggu_lalu']) * 100
                                : 0;
                        ?>
                        <?php if($orderMingguLalu > 0): ?>
                            <p class="mb-0 font-13 text-success"><i class="bx bxs-up-arrow align-middle"></i><?php echo e(number_format($orderMingguLalu, 2)); ?>% from last week</p>
                        <?php else: ?>
                            <p class="mb-0 font-13 text-danger"><i class="bx bxs-down-arrow align-middle"></i><?php echo e(number_format(abs($orderMingguLalu), 2)); ?>% from last week</p>
                        <?php endif; ?>
                    </div>
                    <div class="widgets-icons rounded-circle text-white ms-auto bg-gradient-voilet"><i class="bx bxs-group"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card radius-10">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div>
                        <p class="mb-0 text-secondary">Total Pengiriman</p>
                        <h4 class="my-1"><?php echo e($data['total_pengiriman']); ?></h4>
                        <?php
                            $total_pengirimanMingguLalu = $data['total_pengiriman_minggu_lalu'] > 0
                                ? (($data['total_pengiriman'] - $data['total_pengiriman_minggu_lalu']) / $data['total_pengiriman_minggu_lalu']) * 100
                                : 0;
                        ?>
                        <?php if($total_pengirimanMingguLalu > 0): ?>
                            <p class="mb-0 font-13 text-success"><i class="bx bxs-up-arrow align-middle"></i><?php echo e(number_format($total_pengirimanMingguLalu, 2)); ?>% from last week</p>
                        <?php else: ?>
                            <p class="mb-0 font-13 text-danger"><i class="bx bxs-down-arrow align-middle"></i><?php echo e(number_format(abs($total_pengirimanMingguLalu), 2)); ?>% from last week</p>
                        <?php endif; ?>
                    </div>
                    <div class="widgets-icons rounded-circle text-white ms-auto bg-gradient-branding"><i class="bx bxs-binoculars"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card radius-10">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div>
                        <p class="mb-0 text-secondary">Total Retur</p>
                        <h4 class="my-1"><?php echo e($data['total_retur']); ?></h4>
                        <?php
                            $total_returMingguLalu = $data['total_retur_minggu_lalu'] > 0
                                ? (($data['total_retur'] - $data['total_retur_minggu_lalu']) / $data['total_retur_minggu_lalu']) * 100
                                : 0;
                        ?>
                        <?php if($total_returMingguLalu > 0): ?>
                            <p class="mb-0 font-13 text-success"><i class="bx bxs-up-arrow align-middle"></i><?php echo e(number_format($total_returMingguLalu, 2)); ?>% from last week</p>
                        <?php else: ?>
                            <p class="mb-0 font-13 text-danger"><i class="bx bxs-down-arrow align-middle"></i><?php echo e(number_format(abs($total_returMingguLalu), 2)); ?>% from last week</p>
                        <?php endif; ?>
                    </div>
                    <div class="widgets-icons rounded-circle text-white ms-auto bg-gradient-kyoto"><i class="bx bx-line-chart-down"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--end row-->
<div class="row">
    
    <div class="col-12 col-lg-12 col-xl-12 d-flex">
       <div class="card radius-10 w-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div>
                    <h6 class="mb-0">Statistik Transaksi</h6>
                </div>
                <div class="font-22 ms-auto"><i class="bx bx-dots-horizontal-rounded"></i>
                </div>
            </div>
            <div class="d-flex align-items-center ms-auto font-13 gap-2 my-3">
                
            </div>
           <div class="chart-container-1"><div class="chartjs-size-monitor" style="position: absolute; inset: 0px; overflow: hidden; pointer-events: none; visibility: hidden; z-index: -1;"><div class="chartjs-size-monitor-expand" style="position:absolute;left:0;top:0;right:0;bottom:0;overflow:hidden;pointer-events:none;visibility:hidden;z-index:-1;"><div style="position:absolute;width:1000000px;height:1000000px;left:0;top:0"></div></div><div class="chartjs-size-monitor-shrink" style="position:absolute;left:0;top:0;right:0;bottom:0;overflow:hidden;pointer-events:none;visibility:hidden;z-index:-1;"><div style="position:absolute;width:200%;height:200%;left:0; top:0"></div></div></div>
             <canvas id="chart1" width="672" height="260" style="display: block; width: 672px; height: 260px;" class="chartjs-render-monitor"></canvas>
           </div>
        </div>
        <div class="row row-cols-1 row-cols-md-3 row-cols-xl-3 g-0 row-group text-center border-top">
          <div class="col">
            <div class="p-3">
              <h5 class="mb-0" id="totalRevenue"></h5>
              <small class="mb-0" >Total Revenue</small>
            </div>
          </div>
          <div class="col">
            <div class="p-3">
              <h5 class="mb-0" id="totalTransfer"></h5>
              <small class="mb-0" >Total Transfer</small>
            </div>
          </div>
          <div class="col">
            <div class="p-3">
              <h5 class="mb-0" id="totalCOD"></h5>
              <small class="mb-0">Total COD</small>
            </div>
          </div>
        </div>
       </div>
    </div>

    
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('style'); ?>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
	<script src="<?php echo e(asset('assets/plugins/chartjs/js/Chart.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/index.js')); ?>"></script>
    <script>
        // chart1
        var chart1 = document.getElementById('chart1').getContext('2d');

        var gradientStroke1 = chart1.createLinearGradient(0, 0, 0, 300);
            gradientStroke1.addColorStop(0, '#009efd');
            gradientStroke1.addColorStop(1, '#2af598');

        var gradientStroke2 = chart1.createLinearGradient(0, 0, 0, 300);
            gradientStroke2.addColorStop(0, '#7928ca');
            gradientStroke2.addColorStop(1, '#ff0080');


      $.ajax({
          url: 'dashboard/getDataBarChart', // Replace with the actual route URL
          method: 'GET',
          dataType: 'json',
          success: function(response) {

              var myChart = new Chart(chart1, {
                  type: 'bar',
                  data: {
                      labels: response.labels,
                      datasets: [{
                          label: 'Close Order',
                          data: response.close_order,
                          backgroundColor: Array(response.close_order.length).fill(gradientStroke1),
                          borderColor: Array(response.close_order.length).fill(gradientStroke1),
                          borderWidth: 0,
                          borderRadius: 20
                      },
                      {
                          label: 'Order',
                          data: response.close_order,
                          backgroundColor: Array(response.close_order.length).fill(gradientStroke2),
                          borderColor: Array(response.close_order.length).fill(gradientStroke2),
                          borderWidth: 0,
                          borderRadius: 20
                      }]
                  },
                  options: {
                      maintainAspectRatio: false,
                      barPercentage: 0.7,
                      categoryPercentage: 0.45,
                      plugins: {
                          legend: {
                              maxWidth: 20,
                              boxHeight: 20,
                              position: 'bottom',
                              display: true,
                          }
                      },
                      scales: {
                          x: {
                              stacked: false,
                              beginAtZero: true
                          },
                          y: {
                              stacked: false,
                              beginAtZero: true
                          }
                      }
                  }
              });

              // Update the total revenue, transfer, and COD values
              document.getElementById('totalRevenue').innerText = 'Rp.' + number_format(response.total_revenue, 2, ',', '.');
              document.getElementById('totalTransfer').innerText = 'Rp.' + number_format(response.total_transfer, 2, ',', '.');
              document.getElementById('totalCOD').innerText = 'Rp.' + number_format(response.total_cod, 2, ',', '.');
          },
          error: function(xhr, status, error) {
              console.error('Error fetching data for bar chart:', error);
          }
      });

</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('templates.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\jualinn\resources\views/dashboard.blade.php ENDPATH**/ ?>