<!doctype html>
<html lang="en" class="color-sidebar sidebarcolor1">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <!--favicon-->
    <link rel="icon" href="<?php echo e(asset('assets/images/favicon-32x32.png')); ?>" type="image/png" />
    <!--plugins-->
    <link href="<?php echo e(asset('assets/plugins/simplebar/css/simplebar.css')); ?>" rel="stylesheet" />
    <link href="<?php echo e(asset('assets/plugins/perfect-scrollbar/css/perfect-scrollbar.css')); ?>" rel="stylesheet" />
    <link href="<?php echo e(asset('assets/plugins/metismenu/css/metisMenu.min.css')); ?>" rel="stylesheet" />
    <link href="<?php echo e(asset('assets/plugins/datatable/css/dataTables.bootstrap5.min.css')); ?>" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.17.2/dist/sweetalert2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/lobibox@1.2.7/dist/css/lobibox.min.css">
    <!-- loader-->
    <link href="<?php echo e(asset('assets/css/pace.min.css')); ?>" rel="stylesheet" />
    <script src="<?php echo e(asset('assets/js/pace.min.js')); ?>"></script>
    <!-- Bootstrap CSS -->
    <link href="<?php echo e(asset('assets/css/bootstrap.min.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('assets/css/bootstrap-extended.css')); ?>" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap" rel="stylesheet">
    <link href="<?php echo e(asset('assets/css/app.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('assets/css/icons.css')); ?>" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@3.31.0/dist/tabler-icons.min.css" />
    <!-- Theme Style CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/dark-theme.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/semi-dark.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/header-colors.css')); ?>" />
    <title>
        <?php echo e(env('APP_NAME')); ?>

        <?php if(trim($__env->yieldContent('title'))): ?>
            - <?php echo $__env->yieldContent('title'); ?>
        <?php endif; ?>
    </title>
    <?php echo $__env->yieldPushContent('style'); ?>
</head>

<body>
    <!--wrapper-->
    <div class="wrapper">
        <!--sidebar wrapper -->
        <?php echo $__env->make('templates.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <!--end sidebar wrapper -->
        <!--start header -->
        <?php echo $__env->make('templates.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <!--end header -->
        <!--start page wrapper -->
        <div class="page-wrapper">
            <div class="page-content">
                <?php echo $__env->yieldContent('content'); ?>
            </div>
        </div>
        <!--end page wrapper -->
        <!--start overlay-->
        <div class="overlay toggle-icon"></div>
        <!--end overlay-->
        <!--Start Back To Top Button--> <a href="javaScript:;" class="back-to-top"><i
                class='bx bxs-up-arrow-alt'></i></a>
        <!--End Back To Top Button-->
        <?php echo $__env->make('templates.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    </div>
    <!--end wrapper-->

    <!-- Bootstrap JS -->
    <script src="<?php echo e(asset('assets/js/bootstrap.bundle.min.js')); ?>"></script>
    <!--plugins-->
    <script src="<?php echo e(asset('assets/js/jquery.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/plugins/simplebar/js/simplebar.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/plugins/metismenu/js/metisMenu.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/plugins/perfect-scrollbar/js/perfect-scrollbar.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/plugins/datatable/js/jquery.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/plugins/datatable/js/dataTables.bootstrap5.min.js')); ?>"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.17.2/dist/sweetalert2.all.min.js"></script>
    <script src="<?php echo e(asset('js/number_format.js')); ?>"></script>
    <script src="<?php echo e(asset('js/custom.js')); ?>"></script>
    <script src="<?php echo e(asset('js/currency-formatter.js')); ?>"></script>
    <script src="https://cdn.jsdelivr.net/npm/lobibox@1.2.7/dist/js/lobibox.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/laravel-echo@1.15.0/dist/echo.iife.js"></script>
    <script src="https://js.pusher.com/7.2/pusher.min.js"></script>
    <script>
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        function kosong() {
            swal({
                title: "MAAF !",
                text: "Fitur Belum Bisa Digunakan !!",
                type: "warning",
                timer: 2000,
                showConfirmButton: false
            });
        };

        // window.Echo = new Echo({
        //     broadcaster: 'pusher',
        //     key: 'local',
        //     wsHost: '*************',
        //     wsPort: 8080,
        //     forceTLS: false,
        //     disableStats: true,
        //     enabledTransports: ['ws'],
        // });
        // Echo.private('orders')
        //     .listen('.order.created', (e) => {
        //         console.log('New Order:', e.title, e.body);
        //         const notifItem = `
    //         <a class="dropdown-item" href="javascript:;">
    //             <div class="d-flex align-items-center">
    //                 <div class="notify bg-light-primary text-primary"><i class="bx bx-group"></i></div>
    //                 <div class="flex-grow-1">
    //                     <h6 class="msg-name">${e.order.judul}<span class="msg-time float-end">Baru</span></h6>
    //                     <p class="msg-info">${e.order.pesan}</p>
    //                 </div>
    //             </div>
    //         </a>
    //     `;
        //         document.querySelector('.header-notifications-list').insertAdjacentHTML('afterbegin', notifItem);
        //     });
        // const echo = new Echo({
        //     broadcaster: 'pusher',
        //     key: 'local',
        //     wsHost: '*************',
        //     wsPort: 8080,
        //     forceTLS: false,
        //     encrypted: false,
        //     disableStats: true,
        // });
    </script>
    <?php echo $__env->yieldPushContent('script'); ?>
    <!--app JS-->
    <script src="<?php echo e(asset('assets/js/apps.js')); ?>"></script>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/js/app.js']); ?>
    <script type="module">
        // window.Echo = new Echo({
        //     broadcaster: 'pusher', // atau 'reverb' kalau pakai laravel-reverb
        //     key: 'pydpr6ik6sy49xwrsqy4',
        //     wsHost: window.location.hostname,
        //     wsPort: 8080, // sesuaikan port WebSocket kamu
        //     forceTLS: false,
        //     encrypted: false,
        //     disableStats: true,
        //     enabledTransports: ['ws'],
        // });

        // window.Echo.channel('orders')
        //     .listen('.created', (e) => {
        //         console.log('Notifikasi diterima:', e);
        //         alert('Order baru diterima!');
        //     });
    </script>
</body>

</html>
<?php /**PATH C:\laragon\www\jualinn\resources\views/templates/app.blade.php ENDPATH**/ ?>