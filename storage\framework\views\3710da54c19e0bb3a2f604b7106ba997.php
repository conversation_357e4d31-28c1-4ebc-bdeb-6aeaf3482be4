<script>
    // Create a global object to hold all variant-related functions
    window.ProductVariants = {
        // Initialize the object
        init: function() {
            console.log('ProductVariants initialized');
        },

        // Function to update variants empty state
        updateVariantsEmptyState: function() {
            if ($('#variants-table tbody tr').length === 0) {
                $('#empty-variants-state').show();
                $('#variants-table').hide();
                $('#mass-update-variants').attr('disabled', true);
            } else {
                $('#empty-variants-state').hide();
                $('#variants-table').show();
                $('#mass-update-variants').attr('disabled', false);
            }
        },

        // Function to update attribute summary
        updateAttributeSummary: function() {
            if (selectedAttributes.length === 0) {
                $('#no-attributes-message').show();
                $('#total-combinations').text('0');
                return;
            }

            $('#no-attributes-message').hide();

            let combinationCount = 1;
            let summaryHtml = '';
            let hasValues = false;

            for (const attr of selectedAttributes) {
                const values = attributeValues[attr];
                if (values.length > 0) {
                    hasValues = true;
                    combinationCount *= values.length;

                    summaryHtml += `
                    <div class="mb-3">
                        <h6>${attr}</h6>
                        <div class="attribute-value-pills">
                            ${values.map(val => `<div class="attribute-value-pill">${val}</div>`).join('')}
                        </div>
                    </div>
                `;
                } else {
                    summaryHtml += `
                    <div class="mb-3">
                        <h6>${attr}</h6>
                        <div class="alert alert-warning py-1 px-2">
                            <small><i class='bx bx-error-circle me-1'></i>Belum ada nilai untuk atribut ini</small>
                        </div>
                    </div>
                `;
                }
            }

            $('#attribute-summary-content').html(summaryHtml);
            $('#total-combinations').text(combinationCount);

            // Disable generate button if no combinations possible
            if (combinationCount === 0 || !hasValues) {
                $('#generate-variants-from-summary').attr('disabled', true);
            } else {
                $('#generate-variants-from-summary').attr('disabled', false);
            }
        },

        // Function to add attribute card
        createAttributeCard: function(attributeName) {
            const cardId = 'attr-' + attributeName.toLowerCase().replace(/[^a-z0-9]/g, '-');

            // Check if card already exists
            if ($('#' + cardId).length > 0) {
                console.log('Attribute card already exists:', attributeName);
                return false;
            }

            console.log('Creating attribute card for:', attributeName);

            // Add attribute to selected attributes array if not already there
            if (!selectedAttributes.includes(attributeName)) {
                selectedAttributes.push(attributeName);
            }

            // Initialize attribute values array if not already initialized
            if (!attributeValues[attributeName]) {
                attributeValues[attributeName] = [];
            }

            const cardHtml = `
            <div class="attribute-card mb-3" id="${cardId}" data-attribute="${attributeName}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">${attributeName}</h6>
                    <button type="button" class="btn btn-sm btn-outline-danger delete-attribute">
                        <i class="bx bx-trash"></i>
                    </button>
                </div>
                <div class="card-body">
                    <p class="text-muted small">Tambahkan nilai untuk atribut "${attributeName}"</p>

                    <div class="attribute-value-pills mb-3">
                        <!-- Values will be added here -->
                    </div>

                    <div class="input-group">
                        <input type="text" class="form-control attribute-value-input" placeholder="Nilai baru...">
                        <button class="btn btn-primary add-attribute-value" type="button">
                            <i class="bx bx-plus"></i> Tambah
                        </button>
                    </div>

                    <input type="hidden" name="variant_attributes[]" value="${attributeName}">
                </div>
            </div>
            `;

            $('#attribute-cards').append(cardHtml);
            $('#empty-attributes-state').hide();

            // Update attribute summary
            this.updateAttributeSummary();

            return true;
        },

        // Function to add value to attribute
        addValueToAttribute: function(attributeName, value) {
            const card = $(`#attr-${attributeName.toLowerCase().replace(/[^a-z0-9]/g, '-')}`);
            if (!card.length) return false;

            // Check if value already exists
            if (attributeValues[attributeName].includes(value)) return false;

            // Add value to array
            attributeValues[attributeName].push(value);

            // Add value pill
            const pillHtml = `
            <div class="attribute-value-pill" data-value="${value}">
                ${value}
                <button type="button" class="btn-close ms-2 delete-attribute-value"></button>
                <input type="hidden" name="variant_values[${attributeName}][]" value="${value}">
            </div>
            `;

            card.find('.attribute-value-pills').append(pillHtml);

            // Update attribute summary
            this.updateAttributeSummary();

            return true;
        },

        // Function to add variant row with data
        addVariantRowWithData: function(data) {
            console.log('Adding variant row with data:', data);

            try {
                const rowIndex = $('#variants-table tbody tr').length;

                // Ensure data.combination is an object before using Object.entries
                const combination = data.combination || {};
                if (typeof combination !== 'object' || Object.keys(combination).length === 0) {
                    console.warn('Invalid combination object:', combination);
                }

                const combinationString = Object.entries(combination).map(([attr, val]) => `${attr}: ${val}`).join(', ');
                const combinationJson = JSON.stringify(combination);

                // Log attribute value IDs for debugging
                console.log('Current attributeValueIds:', window.attributeValueIds);

                // Build attribute value inputs
                let attributeInputs = '';

                // If we have explicit attribute_value_ids from the server, use those
                if (data.attribute_value_ids && Array.isArray(data.attribute_value_ids) && data.attribute_value_ids.length > 0) {
                    console.log('Using provided attribute value IDs:', data.attribute_value_ids);

                    // Add the attribute values from the combination
                    Object.entries(combination).forEach(([attr, val]) => {
                        attributeInputs += `
                        <input type="hidden" name="variants[${rowIndex}][attribute_values][${attr}]" value="${val}">
                        `;
                    });

                    // Add the attribute value IDs
                    data.attribute_value_ids.forEach(id => {
                        if (id) { // Only add if ID is not empty
                            attributeInputs += `
                            <input type="hidden" name="variants[${rowIndex}][attribute_value_ids][]" value="${id}">
                            `;
                        }
                    });
                } else {
                    // Otherwise, try to find the IDs from the global attributeValueIds object
                    Object.entries(combination).forEach(([attr, val]) => {
                        // Find the attribute value ID from the global attributeValueIds object
                        let attributeValueId = '';

                        if (window.attributeValueIds &&
                            window.attributeValueIds[attr] &&
                            window.attributeValueIds[attr][val]) {
                            attributeValueId = window.attributeValueIds[attr][val];
                            console.log(`Found ID for ${attr}:${val} = ${attributeValueId}`);
                        } else {
                            console.warn(`No ID found for ${attr}:${val}`);

                            // Try to find the attribute and value in the DOM
                            const attrCard = $(`#attr-${attr.toLowerCase().replace(/[^a-z0-9]/g, '-')}`);
                            if (attrCard.length) {
                                const valuePill = attrCard.find(`.attribute-value-pill[data-value="${val}"]`);
                                if (valuePill.length) {
                                    const hiddenInput = valuePill.find('input[type="hidden"]');
                                    if (hiddenInput.length && hiddenInput.val()) {
                                        attributeValueId = hiddenInput.val();
                                        console.log(`Found ID in DOM for ${attr}:${val} = ${attributeValueId}`);
                                    }
                                }
                            }
                        }

                        attributeInputs += `
                        <input type="hidden" name="variants[${rowIndex}][attribute_values][${attr}]" value="${val}">
                        `;

                        if (attributeValueId) {
                            attributeInputs += `
                            <input type="hidden" name="variants[${rowIndex}][attribute_value_ids][]" value="${attributeValueId}">
                            `;
                        }
                    });
                }

                // Get price values with fallbacks
                let variantPrice = '';
                let variantSalePrice = '';

                // Handle price values
                if (data.price !== undefined && data.price !== null) {
                    variantPrice = data.price;
                } else {
                    // Try to get from regular_price input
                    const regularPriceInput = $('#regular_price');
                    if (regularPriceInput.length && regularPriceInput.val()) {
                        variantPrice = regularPriceInput.val();
                    } else {
                        variantPrice = 0;
                    }
                }

                // Handle sale price values
                if (data.discount_price !== undefined && data.discount_price !== null && data.discount_price !== '') {
                    variantSalePrice = data.discount_price;
                } else {
                    // Try to get from discount_price input
                    const salePriceInput = $('#discount_price');
                    if (salePriceInput.length && salePriceInput.val()) {
                        variantSalePrice = salePriceInput.val();
                    } else {
                        variantSalePrice = '';
                    }
                }

                console.log('Using variant price:', variantPrice);
                console.log('Using variant sale price:', variantSalePrice);

                // Create a unique ID for the variant row for easier reference
                const variantRowId = data.id ? `variant-row-${data.id}` : `variant-row-new-${rowIndex}`;

                const rowHtml = `
    <tr id="${variantRowId}" class="variant-row" data-index="${rowIndex}">
        <td>
            <div class="variant-combination-display fw-medium">${combinationString}</div>
            ${data.id ? `<input type="hidden" name="variants[${rowIndex}][id]" value="${data.id}">` : ''}
            <input type="hidden" name="variants[${rowIndex}][combination]" value='${combinationJson}'>
            ${attributeInputs}
        </td>
        <td>
            <input type="text" class="form-control variant-code" name="variants[${rowIndex}][variant_code]" value="${data.variant_code || ''}" placeholder="Auto">
        </td>
        <td>
            <div class="input-group">
                <span class="input-group-text">Rp</span>
                <input type="text" class="form-control price-input variant-price" name="variants[${rowIndex}][price]" value="${variantPrice}" min="0" required>
            </div>
        </td>
        <td>
            <div class="input-group">
                <span class="input-group-text">Rp</span>
                <input type="text" class="form-control price-input variant-sale-price" name="variants[${rowIndex}][discount_price]" value="${variantSalePrice}" min="0">
            </div>
        </td>
        <td>
            <button type="button" class="btn btn-outline-danger btn-sm delete-variant" title="Hapus Variasi">
                <i class="bx bx-trash"></i>
            </button>
        </td>
    </tr>
    `;

                $('#variants-table tbody').append(rowHtml);

                // Show the variants table
                $('#empty-variants-state').hide();
                $('#variants-table').show();
                $('#mass-update-variants').attr('disabled', false);

                // Update variant count if available
                if ($('#variant-count').length) {
                    $('#variant-count').text($('#variants-table tbody tr').length);
                    $('#variant-count-summary').show();
                }

                return $('#variants-table tbody tr').last();
            } catch (error) {
                console.error('Error adding variant row:', error);
                return null;
            }
        }
    };

    // Global object to store attribute value IDs
    window.attributeValueIds = {};

    // Function to fetch attribute value IDs from the server
    function fetchAttributeValueIds() {
        console.log('Fetching attribute value IDs from server...');

        // If we already have attribute value IDs from the server-side rendering with sufficient data, don't fetch again
        if (window.attributeValueIds && Object.keys(window.attributeValueIds).length > 0) {
            // Check if we have IDs for all selected attributes and their values
            let hasAllIds = true;
            for (const attr of selectedAttributes) {
                if (!window.attributeValueIds[attr]) {
                    hasAllIds = false;
                    break;
                }

                // Check if we have IDs for all values of this attribute
                if (attributeValues[attr]) {
                    for (const val of attributeValues[attr]) {
                        if (!window.attributeValueIds[attr][val]) {
                            hasAllIds = false;
                            break;
                        }
                    }
                }

                if (!hasAllIds) break;
            }

            if (hasAllIds) {
                console.log('Using existing attribute value IDs (all values covered):', window.attributeValueIds);
                return;
            } else {
                console.log('Some attribute values are missing IDs, fetching from server...');
            }
        }

        $.ajax({
            url: '/api/product-attributes/values',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Merge with existing IDs if any
                    if (window.attributeValueIds && Object.keys(window.attributeValueIds).length > 0) {
                        // Merge the objects
                        for (const attr in response.data) {
                            if (!window.attributeValueIds[attr]) {
                                window.attributeValueIds[attr] = {};
                            }

                            for (const val in response.data[attr]) {
                                window.attributeValueIds[attr][val] = response.data[attr][val];
                            }
                        }
                        console.log('Merged attribute value IDs:', window.attributeValueIds);
                    } else {
                        window.attributeValueIds = response.data;
                        console.log('Loaded attribute value IDs from API:', window.attributeValueIds);
                    }

                    // Update any existing variant rows with the new IDs
                    updateVariantRowsWithAttributeValueIds();
                }
            },
            error: function(xhr) {
                console.error('Failed to load attribute value IDs:', xhr);
            }
        });
    }

    // Function to update existing variant rows with attribute value IDs
    function updateVariantRowsWithAttributeValueIds() {
        $('#variants-table tbody tr').each(function(rowIndex) {
            const row = $(this);
            const combinationInput = row.find('input[name^="variants"][name$="[combination]"]');

            if (combinationInput.length) {
                try {
                    const combination = JSON.parse(combinationInput.val());

                    // Check each attribute-value pair
                    Object.entries(combination).forEach(([attr, val]) => {
                        if (window.attributeValueIds &&
                            window.attributeValueIds[attr] &&
                            window.attributeValueIds[attr][val]) {

                            const attributeValueId = window.attributeValueIds[attr][val];

                            // Look for empty attribute value ID inputs
                            const emptyInputs = row.find(`input[name="variants[${rowIndex}][attribute_value_ids][]"][value=""]`);

                            if (emptyInputs.length) {
                                // Update the first empty input
                                console.log(`Updating attribute value ID for ${attr}:${val} to ${attributeValueId}`);
                                emptyInputs.first().val(attributeValueId);
                            } else {
                                // Check if we already have this attribute value ID
                                const existingInput = row.find(`input[name="variants[${rowIndex}][attribute_value_ids][]"][value="${attributeValueId}"]`);

                                if (!existingInput.length) {
                                    // If not, add a new input
                                    console.log(`Adding new attribute value ID input for ${attr}:${val} = ${attributeValueId}`);
                                    row.find('input[name^="variants"][name$="[combination]"]').after(
                                        `<input type="hidden" name="variants[${rowIndex}][attribute_value_ids][]" value="${attributeValueId}">`
                                    );
                                }
                            }
                        }
                    });
                } catch (e) {
                    console.error('Error parsing combination JSON:', e);
                }
            }
        });
    }

    // Initialize the ProductVariants object when the document is ready
    $(document).ready(function() {
        console.log('ProductVariants initialization started');

        // Make sure the global object is available
        if (!window.ProductVariants) {
            console.error('ProductVariants object is not defined. Recreating it.');
            window.ProductVariants = {
                // Copy all the methods from the object defined above
                init: function() {
                    console.log('ProductVariants initialized (fallback)');
                },
                updateVariantsEmptyState: function() {
                    if ($('#variants-table tbody tr').length === 0) {
                        $('#empty-variants-state').show();
                        $('#variants-table').hide();
                        $('#mass-update-variants').attr('disabled', true);
                    } else {
                        $('#empty-variants-state').hide();
                        $('#variants-table').show();
                        $('#mass-update-variants').attr('disabled', false);
                    }
                },
                updateAttributeSummary: function() {
                    // Implementation copied from above
                    if (selectedAttributes.length === 0) {
                        $('#no-attributes-message').show();
                        $('#total-combinations').text('0');
                        return;
                    }

                    $('#no-attributes-message').hide();

                    let combinationCount = 1;
                    let summaryHtml = '';
                    let hasValues = false;

                    for (const attr of selectedAttributes) {
                        const values = attributeValues[attr];
                        if (values && values.length > 0) {
                            hasValues = true;
                            combinationCount *= values.length;

                            summaryHtml += `
                            <div class="mb-3">
                                <h6>${attr}</h6>
                                <div class="attribute-value-pills">
                                    ${values.map(val => `<div class="attribute-value-pill">${val}</div>`).join('')}
                                </div>
                            </div>`;
                        } else {
                            summaryHtml += `
                            <div class="mb-3">
                                <h6>${attr}</h6>
                                <div class="alert alert-warning py-1 px-2">
                                    <small><i class='bx bx-error-circle me-1'></i>Belum ada nilai untuk atribut ini</small>
                                </div>
                            </div>`;
                        }
                    }

                    $('#attribute-summary-content').html(summaryHtml);
                    $('#total-combinations').text(combinationCount);

                    // Disable generate button if no combinations possible
                    if (combinationCount === 0 || !hasValues) {
                        $('#generate-variants-from-summary').attr('disabled', true);
                    } else {
                        $('#generate-variants-from-summary').attr('disabled', false);
                    }
                }
            };
        }

        // Initialize
        if (typeof window.ProductVariants.init === 'function') {
            window.ProductVariants.init();
            console.log('ProductVariants initialized successfully');
        } else {
            console.error('ProductVariants.init is not a function');
        }

        // Fetch attribute value IDs
        fetchAttributeValueIds();

        // Ensure the generate button is properly enabled/disabled
        if (typeof window.ProductVariants.updateAttributeSummary === 'function') {
            window.ProductVariants.updateAttributeSummary();
        }
    });
</script>
<?php /**PATH C:\laragon\www\jualinn\resources\views/data-master/product/scripts/product-variants-globals.blade.php ENDPATH**/ ?>