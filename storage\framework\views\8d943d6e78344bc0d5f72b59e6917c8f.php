<?php $__env->startSection('title', $title); ?>
<?php $__env->startSection('content'); ?>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>
        .cost-input {
            width: 100%;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: right;
        }

        .cost-input:focus {
            border-color: #4e73df;
            outline: none;
        }
    </style>
    <h6 class="mb-0 text-uppercase"><?php echo e($title); ?></h6>
    <hr />
    <div class="card">
        <div class="card-body">     
            <div class="row mb-3">
                <?php if(Auth::user()->level_user == '1'): ?> 
                <div class="col-md-4">
                    <select class="form-select" id="timAds" name="timAds">
                        <option value="">Pilih Tim ADS</option>
                        <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($user->id); ?>"><?php echo e($user->name); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <?php endif; ?>
                <div class="col-md-2">
                    <input type="text" name="filter-bulan" id="filter-bulan" value="<?php echo e(date('Y-m')); ?>"
                           class="form-control datepicker" placeholder="Pilih bulan" />
                </div>
                <div class="col-md-4 text-end d-flex align-items-start justify-content-end">
                    <button id="btnEarningPotential" class="btn btn-primary mt-2">
                        <i class="bx bx-folder-open"></i>
                        Earning Potential
                    </button>
                </div>
            </div>
            <div class="table-responsive">
                <table id="tabelEarningPotential" class="table datatable table-striped" style="width:100%">
                    <thead>
                        <tr>
                            <th rowspan="2">No</th>                            
                            <th rowspan="2">TANGGAL</th>
                            <th rowspan="2">LEAD</th>
                            <th colspan="2" class="text-center">LEAD</th>
                            <th colspan="2" class="text-center">CLOSE TF</th>
                            <th colspan="2" class="text-center">CLOSE COD</th>
                            <th rowspan="2">CR</th>
                            <th rowspan="2">IKLAN</th>
                            <th rowspan="2">CPR</th>
                            <th rowspan="2">RETUR</th>
                            <th rowspan="2">O EXPENSE</th>
                        </tr>
                        <tr>
                            <th>TF</th>
                            <th>COD</th>
                            <th>PROFIT</th>
                            <th>QTY</th>
                            <th>PROFIT</th>
                            <th>QTY</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
        
    </div>
    <div id="modalContainer"></div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script'); ?>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/plugins/monthSelect/style.css">
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.js"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/plugins/monthSelect/index.js"></script>

<script>
    $(document).ready(function () {

        $(".datepicker").flatpickr({
            disableMobile: "true",
            plugins: [
                new monthSelectPlugin({
                    shorthand: true,
                    dateFormat: "Y-m",
                    altFormat: "F Y",
                    theme: "material_blue"
                })
            ]
        });

        function formatCurrency(value) {
            if (value === null || value === undefined) return 'Rp 0';
            return new Intl.NumberFormat('id-ID', {
                style: 'currency',
                currency: 'IDR',
                minimumFractionDigits: 0
            }).format(value);
        }

        function parseCurrency(value) {
            return parseInt(value.replace(/[^0-9]/g, '') || 0);
        }

        // let timAds = $('#timAds option:selected').val();
        // let bulan = $('#filter-bulan').val();

        let table = $('#tabelEarningPotential').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: "<?php echo e(route('data-master.earning-potential.data')); ?>",
                data: function(d) {
                    d.handled_by = $('#timAds').val();
                    d.bulan = $('#filter-bulan').val();
                }
            },
            columns: [
                { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
                { data: 'date' },
                { data: 'total_orders' },
                { data: 'lead_tf' },
                { data: 'lead_cod' },
                {
                    data: 'total_profit_tf',
                    render: data => formatCurrency(data)
                },
                {
                    data: 'close_order_tf',
                    render: data => data || 0
                },
                {
                    data: 'total_profit_cod',
                    render: data => formatCurrency(data)
                },
                {
                    data: 'close_order_cod',
                    render: data => data || 0
                },
                {
                    data: 'closer_ratio_day',
                    render: data => (data ? Number(data).toFixed(2) + '%' : '0%')
                },
                {
                    data: 'advertising_cost',
                    render: function (data, type, row) {
                        if (type === 'display') {
                            let isDisabled = "<?php echo e(Auth::user()->level_user == '1' ? '' : 'disabled'); ?>";
                            return `
                                <input type="text" class="cost-input" data-field="advertising_cost" data-date="${row.date}"
                                    value="${formatCurrency(data)}" ${isDisabled}
                                    title="<?php echo e(Auth::user()->level_user != 1 ? 'Hanya admin yang bisa edit' : ''); ?>">
                            `;
                        }
                        return data;
                    }
                },
                {
                    data: 'cpr',
                    render: data => data ? new Intl.NumberFormat('id-ID').format(data) : 0
                },
                {
                    data: 'retur',
                    render: data => data || 0
                },
                {
                    data: 'other_cost',
                    render: function (data, type, row) {
                        if (type === 'display') {
                            let isDisabled = "<?php echo e(Auth::user()->level_user == '1' ? '' : 'disabled'); ?>";
                            return `
                                <input type="text" class="cost-input" data-field="other_cost" data-date="${row.date}"
                                    value="${formatCurrency(data)}" ${isDisabled}
                                    title="<?php echo e(Auth::user()->level_user != 1 ? 'Hanya admin yang bisa edit' : ''); ?>">
                            `;
                        }
                        return data;
                    }
                }
            ]
        });

        $('#timAds').change(function () {
            table.ajax.reload();
        });

        $('#filter-bulan').change(function () {
            table.ajax.reload();
        });

        $(document).on('focus', '.cost-input', function () {
            const value = parseCurrency($(this).val());
            $(this).val(value === 0 ? '' : value.toString())
                .data('original-value', value);
        });

        $(document).on('blur', '.cost-input', function () {
            const input = $(this);
            const date = input.data('date');
            const field = input.data('field');
            const rawValue = input.val();
            const newValue = parseCurrency(rawValue);
            const originalValue = input.data('original-value') || 0;
            const handledBy = $('#timAds').val();

            input.val(formatCurrency(newValue));

            if (newValue !== originalValue) {
                input.prop('disabled', true).after('<span class="loading-spinner"></span>');

                $.ajax({
                    url: "<?php echo e(route('data-master.earning-potential.update')); ?>",
                    method: 'POST',
                    data: {
                        _token: "<?php echo e(csrf_token()); ?>",
                        date: date,
                        handled_by: handledBy,
                        field: field,
                        value: newValue
                    },
                    success: function (res) {
                        if (res.success) {
                            input.removeClass('is-invalid').addClass('is-valid');
                            setTimeout(() => input.removeClass('is-valid'), 2000);
                            table.ajax.reload(null, false);
                        }
                    },
                    error: function () {
                        input.val(formatCurrency(originalValue))
                            .removeClass('is-valid').addClass('is-invalid');
                    },
                    complete: function () {
                        input.prop('disabled', false)
                            .next('.loading-spinner').remove();
                    }
                });
            }
        });

        $('#btnEarningPotential').click(function () {
            $.get("<?php echo e(route('data-master.earning-potential.create')); ?>", function (data) {
                $('#modalContainer').html(data);
                $('#modalEarningPotential').modal('show');                
            });
        });        
    });
</script>

<?php $__env->stopPush(); ?>

<?php echo $__env->make('templates.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\jualinn\resources\views/data-master/earning-potential/index.blade.php ENDPATH**/ ?>