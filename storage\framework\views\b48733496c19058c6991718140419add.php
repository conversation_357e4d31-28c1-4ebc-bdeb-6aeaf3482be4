<?php $__env->startSection('title', $title); ?>
<?php $__env->startSection('content'); ?>
    <style>
        #tabelMateriIklan{
            text-align: center;  /* Mengatur teks header agar berada di tengah */
        }
        .action-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }
    </style>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <h6 class="mb-0 text-uppercase"><?php echo e($title); ?></h6>
    <hr/>
    <div class="card">
        <div class="card-body">
            <div class="col-md-6">
                <div class="action-container">
                    <button id="btnTambahMateriIklan" class="btn btn-primary mb-2">
                        <i class="bx bx-group"></i>
                        Tambah <?php echo e($title); ?>

                    </button>
                </div>
            </div>
            <div class="col-md-3 d-inline-block">
                <input type="text" id="dateRangePicker" class="form-control mb-2" value="<?php echo e(date('Y-m-01') . ' to ' . date('Y-m-t')); ?>" placeholder="Select Date Range">
            </div>
            <div class="table-responsive">
                <table id="tabelMateriIklan" class="table datatable table-striped" style="width:100%">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Judul Materi</th>
                            <th>Deskripsi Iklan</th>
                            <th>Text Iklan</th>
                            <th>Platform</th>
                            <th>Hastag</th>
                            <th>Status</th>                            
                            <th>Aksi</th>                            
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
    <div id="modalContainer"></div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script'); ?>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.js"></script>
    <script>
        
        $(document).ready(function() {
            flatpickr("#dateRangePicker", {
                mode: "range",
                dateFormat: "Y-m-d",
            });
            var date = $("#dateRangePicker").val();  
      
            // Inisialisasi DataTable Pegawai
            let table = $('#tabelMateriIklan').DataTable({
                ajax: {
                    url: "<?php echo e(route('ads.materi-iklan.data')); ?>",
                    data: function(d) {
                        d.date = date;
                    },
                },
                processing: true,
                serverSide: true,                
                columns: [
                    { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false }, // Kolom nomor urut
                    { data: 'judul_materi', render: data => data ?? '-' },
                    { data: 'deskripsi_iklan', render: data => data ?? '-' },
                    { data: 'hastag', render: data => data ?? '-' },
                    { data: 'headline', render: data => data ?? '-' },
                    { data: 'platform', render: data => data ?? '-' },
                    { data: 'status', render: data => data ?? '-' },                    
                    { data: 'action', orderable: false, searchable: false } // Kolom aksi tidak perlu disortir
                ]
            });

            //dateRangePicker
            $('#dateRangePicker').change(function() {
                if ($(this).val().split(' to ').length === 2) {
                    date = $(this).val();
                    table.draw();
                }
            });

            // Event: Klik tombol Tambah
            $('#btnTambahMateriIklan').click(function() {
                $.get("<?php echo e(route('ads.materi-iklan.create')); ?>", function(data) {
                    $('#modalContainer').html(data);
                    $('#modalCreateMateriIklan').modal('show');

                    // === Gunakan Fungsi Reusable untuk Simpan Data ===
                    $(document).off('click', '#btnSimpandata').on('click', '#btnSimpandata', function() {
                        simpanData(
                            '#formMateriIklan',
                            '#modalCreateMateriIklan',
                            "<?php echo e(route('ads.materi-iklan.store')); ?>",
                            table
                        );
                    });
                });
            }); 
            
            // Event: Klik tombol Edit
            $('#tabelMateriIklan').on('click', '.btn-edit', function() {
                var id = $(this).data('id');
                var url = "<?php echo e(route('ads.materi-iklan.edit', ':id')); ?>".replace(':id', id);
                $.get(url, function(data) {
                    $('#modalContainer').html(data);
                    $('#modalCreateMateriIklan').modal('show');

                    // === Gunakan Fungsi Reusable untuk Simpan Data ===
                    $(document).off('click', '#btnSimpandata').on('click', '#btnSimpandata', function () {
                        simpanData(
                            '#formMateriIklan',
                            '#modalCreateMateriIklan',
                            "<?php echo e(route('ads.materi-iklan.update', ':id')); ?>".replace(':id', id),
                            table
                        );
                    });
                });
            });
            
            // Event: Klik tombol Hapus
            $('#tabelMateriIklan').on('click', '.btn-delete', function() {
                let id = $(this).data('id');
                let url = "<?php echo e(route('ads.materi-iklan.destroy', ':id')); ?>".replace(':id', id);
                confirmDelete(url, table)
            });
            
             // Event: Klik tombol status materi iklan
            $('#tabelMateriIklan').on('click', '.btn-status', function() {
                Swal.fire({
                    title: "Are you sure?",
                    text: "Change status?",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Yes, update it!"
                }).then((result) => {
                    if (result.isConfirmed) {
                        let id = $(this).data('id');
                        simpanData(
                            '',
                            '',
                            "<?php echo e(route('ads.materi-iklan.update-status', ':id')); ?>".replace(':id', id),
                            table
                        );
                    } else if (
                        /* Read more about handling dismissals below */
                        result.dismiss === Swal.DismissReason.cancel
                    ) {
                        Swal.fire({
                            title: "Cancelled",
                            text: "Status update was cancelled.",
                            icon: "error"
                        });
                    }
                });
            });
        });       

    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('templates.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\jualinn\resources\views/materi-iklan/index.blade.php ENDPATH**/ ?>