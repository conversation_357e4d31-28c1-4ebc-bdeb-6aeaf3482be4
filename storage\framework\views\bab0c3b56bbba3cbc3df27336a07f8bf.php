<?php $__env->startSection('title', $title); ?>

<?php $__env->startSection('content'); ?>
    <h6 class="mb-0 text-uppercase">Landing Page Builder</h6>
    <hr />
    <form id="landingPageForm" action="<?php echo e(route('landingPage.store')); ?>" method="POST" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>

        <!-- Hidden fields for pixel tracking configuration -->
        <input type="hidden" id="facebook_pixel_id_config" name="facebook_pixel_id">
        <input type="hidden" id="pixel_events_config" name="pixel_events">
        <input type="hidden" id="pixel_event_parameters_config" name="pixel_event_parameters">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <!-- Left sidebar navigation -->
                    <div class="col-md-3 pe-4">
                        <div class="sidebar-nav-container">
                            <ul class="nav nav-pills flex-column" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active d-flex align-items-center mb-3 text-start section-nav" id="v-pills-section1-tab" data-bs-toggle="pill" data-bs-target="#v-pills-section1" type="button" role="tab">
                                        <span class="section-number me-3">1</span>
                                        <span class="section-title">Section 1</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center mb-3 text-start section-nav" id="v-pills-section2-tab" data-bs-toggle="pill" data-bs-target="#v-pills-section2" type="button" role="tab">
                                        <span class="section-number me-3">2</span>
                                        <span class="section-title">Section 2</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center mb-3 text-start section-nav" id="v-pills-section3-tab" data-bs-toggle="pill" data-bs-target="#v-pills-section3" type="button" role="tab">
                                        <span class="section-number me-3">3</span>
                                        <span class="section-title">Section 3</span>
                                        <span class="badge bg-info ms-auto optional-badge">Optional</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center mb-3 text-start section-nav" id="v-pills-section4-tab" data-bs-toggle="pill" data-bs-target="#v-pills-section4" type="button" role="tab">
                                        <span class="section-number me-3">4</span>
                                        <span class="section-title">Section 4</span>
                                        <span class="badge bg-info ms-auto optional-badge">Optional</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center mb-3 text-start section-nav" id="v-pills-section5-tab" data-bs-toggle="pill" data-bs-target="#v-pills-section5" type="button" role="tab">
                                        <span class="section-number me-3">5</span>
                                        <span class="section-title">Section 5</span>
                                        <span class="badge bg-info ms-auto optional-badge">Optional</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center mb-3 text-start section-nav" id="v-pills-section6-tab" data-bs-toggle="pill" data-bs-target="#v-pills-section6" type="button" role="tab">
                                        <span class="section-number me-3">6</span>
                                        <span class="section-title">Section 6</span>
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- Main content area -->
                    <div class="col-md-9 ps-4">
                        <div class="tab-content" id="v-pills-tabContent">
                            <!-- Section 1 Content -->
                            <div class="tab-pane fade show active" id="v-pills-section1" role="tabpanel" aria-labelledby="v-pills-section1-tab" tabindex="0">
                                <h4 class="mb-4"><i class="bx bx-home me-2"></i> Landing Page Section 1</h4>
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title"><i class="bx bx-package me-2"></i> Product</h5>

                                        <!-- Landing Page Name -->
                                        <div class="mb-3">
                                            <label for="name" class="form-label">Nama Landing Page <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="name" name="name" placeholder="Masukkan nama landing page" required>
                                        </div>

                                        <!-- Product Selection (Read-only) -->
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="product_display" class="form-label">Produk Terpilih <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="product_display" name="product_display" placeholder="Produk akan dimuat dari pilihan sebelumnya" readonly>
                                                    <input type="hidden" id="product_id" name="product_id" required>
                                                    <div class="form-text">Produk dipilih dari halaman sebelumnya dan tidak dapat diubah</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <!-- Background Color -->
                                                <div class="mb-3">
                                                    <label for="background_color" class="form-label">Background Color <span class="text-danger">*</span></label>
                                                    <div class="input-group">
                                                        <input type="color" class="form-control form-control-color" id="background_color" name="background_color" value="#DCE8FD" title="Choose background color">
                                                        <input type="text" class="form-control" id="background_color_text" value="#DCE8FD" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Landing Page Title -->
                                        <div class="mb-3">
                                            <label for="main_title" class="form-label">Judul Landing Page <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="main_title" name="main_title" placeholder="Masukkan judul landing page" required>
                                        </div>

                                        <!-- Sub Section Title -->
                                        <div class="mb-3">
                                            <label for="sub_title" class="form-label">Sub Judul Section</label>
                                            <input type="text" class="form-control" id="sub_title" name="sub_title" placeholder="Masukkan sub judul section">
                                        </div>

                                        <!-- Image Upload -->
                                        <div class="mb-3">
                                            <label for="section_image" class="form-label">Upload Gambar (564 x 2030) <span class="text-danger">*</span></label>
                                            <input type="file" class="form-control" id="section_image" name="section_image" accept="image/*" required>
                                            <div class="form-text">Format yang didukung: JPG, PNG, GIF. Ukuran maksimal: 2MB</div>
                                            <div id="image_preview" class="mt-2" style="display: none;">
                                                <img id="preview_img" src="" alt="Preview" class="img-thumbnail" style="max-width: 200px;">
                                            </div>
                                        </div>

                                        <!-- Content Description -->
                                        <div class="mb-3">
                                            <label for="content_description" class="form-label">Keterangan</label>
                                            <div id="content_description" style="height: 200px;"></div>
                                            <input type="hidden" id="content_description_hidden" name="content_description">
                                            <div class="form-text">Deskripsi konten untuk section ini</div>
                                        </div>

                                        <!-- Pixel Configuration Status -->
                                        <div class="mb-3">
                                            <div class="card border-info">
                                                <div class="card-body p-3">
                                                    <h6 class="card-title mb-2">
                                                        <i class="bx bx-target-lock me-2"></i>Status Facebook Pixel Tracking
                                                    </h6>
                                                    <div id="pixel-status" class="d-flex align-items-center">
                                                        <span class="badge bg-secondary me-2">
                                                            <i class="bx bx-loader-alt bx-spin me-1"></i>Loading...
                                                        </span>
                                                        <small class="text-muted">Memuat konfigurasi pixel...</small>
                                                    </div>
                                                    <div class="form-text mt-2">
                                                        <small>
                                                            <strong>Info:</strong> Pixel tracking bersifat opsional.
                                                            Anda bisa mulai tanpa pixel, atau upgrade bertahap dari basic ke advanced tracking.
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Section 2 Content -->
                            <div class="tab-pane fade" id="v-pills-section2" role="tabpanel" aria-labelledby="v-pills-section2-tab" tabindex="0">
                                <h4 class="mb-4"><i class="bx bx-list-ul me-2"></i> Landing Page Section 2</h4>
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title"><i class="bx bx-package me-2"></i> Product</h5>

                                        <!-- Product Selection and Background Color -->
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="section2_product_id" class="form-label">Produk</label>
                                                    <input type="text" class="form-control" id="section2_product_display" placeholder="Produk akan mengikuti pilihan di Section 1" readonly>
                                                    <input type="hidden" id="section2_product_id" name="section2_product_id">
                                                    <div class="form-text">Produk akan otomatis mengikuti produk yang dipilih di Section 1</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <!-- Background Color -->
                                                <div class="mb-3">
                                                    <label for="section2_background_color" class="form-label">Background Color <span class="text-danger">*</span></label>
                                                    <div class="input-group">
                                                        <input type="color" class="form-control form-control-color" id="section2_background_color" name="section2_background_color" value="#DCE8FD" title="Choose background color">
                                                        <input type="text" class="form-control" id="section2_background_color_text" value="#DCE8FD" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Section Title -->
                                        <div class="mb-3">
                                            <label for="section2_title" class="form-label">Judul Section</label>
                                            <input type="text" class="form-control" id="section2_title" name="section2_title" placeholder="Masukkan judul section">
                                        </div>

                                        <!-- Sub Section Title -->
                                        <div class="mb-3">
                                            <label for="section2_sub_title" class="form-label">Sub Judul Section</label>
                                            <input type="text" class="form-control" id="section2_sub_title" name="section2_sub_title" placeholder="Masukkan sub judul section">
                                        </div>

                                        <!-- Image Upload -->
                                        <div class="mb-3">
                                            <label for="section2_image" class="form-label">Upload Gambar (564 x 2030)</label>
                                            <input type="file" class="form-control" id="section2_image" name="section2_image" accept="image/*">
                                            <div class="form-text">Format yang didukung: JPG, PNG, GIF. Ukuran maksimal: 2MB</div>
                                            <div id="section2_image_preview" class="mt-2" style="display: none;">
                                                <img id="section2_preview_img" src="" alt="Preview" class="img-thumbnail" style="max-width: 200px;">
                                            </div>
                                        </div>

                                        <!-- Content Description -->
                                        <div class="mb-3">
                                            <label for="section2_content" class="form-label">Keterangan</label>
                                            <div id="section2_content" style="height: 200px;"></div>
                                            <input type="hidden" id="section2_content_hidden" name="section2_content">
                                            <div class="form-text">Deskripsi konten untuk section ini</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Section 3 Content -->
                            <div class="tab-pane fade" id="v-pills-section3" role="tabpanel" aria-labelledby="v-pills-section3-tab" tabindex="0">
                                <h4 class="mb-4"><i class="bx bx-cog me-2"></i> Landing Page Section 3 <span class="badge bg-secondary">Optional</span></h4>

                                <div class="card">
                                    <div class="card-body">
                                        <!-- Section 3 Enable Toggle -->
                                        <div class="alert alert-info d-flex align-items-center mb-4">
                                            <div class="form-check form-switch me-3">
                                                <input class="form-check-input" type="checkbox" id="section3_enabled" name="section3_enabled" onchange="toggleSectionFields(3)">
                                                <label class="form-check-label" for="section3_enabled">
                                                    <i class="bx bx-toggle-left me-1"></i>Aktifkan Section 3
                                                </label>
                                            </div>
                                            <div class="flex-grow-1">
                                                <small class="mb-0">Aktifkan untuk menampilkan section 3 di landing page</small>
                                            </div>
                                        </div>

                                        <div id="section3_fields">
                                            <h5 class="card-title"><i class="bx bx-package me-2"></i> Product</h5>

                                            <!-- Product Selection and Background Color -->
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="section3_product_id" class="form-label">Produk</label>
                                                        <input type="text" class="form-control" id="section3_product_display" placeholder="Produk akan mengikuti pilihan di Section 1" readonly>
                                                        <input type="hidden" id="section3_product_id" name="section3_product_id">
                                                        <div class="form-text">Produk akan otomatis mengikuti produk yang dipilih di Section 1</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <!-- Background Color -->
                                                    <div class="mb-3">
                                                        <label for="section3_background_color" class="form-label">Background Color</label>
                                                        <div class="input-group">
                                                            <input type="color" class="form-control form-control-color" id="section3_background_color" name="section3_background_color" value="#DCE8FD" title="Choose background color">
                                                            <input type="text" class="form-control" id="section3_background_color_text" value="#DCE8FD" readonly>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Section Title -->
                                            <div class="mb-3">
                                                <label for="section3_title" class="form-label">Judul Section</label>
                                                <input type="text" class="form-control" id="section3_title" name="section3_title" placeholder="Masukkan judul section">
                                            </div>

                                            <!-- Sub Section Title -->
                                            <div class="mb-3">
                                                <label for="section3_sub_title" class="form-label">Sub Judul Section</label>
                                                <input type="text" class="form-control" id="section3_sub_title" name="section3_sub_title" placeholder="Masukkan sub judul section">
                                            </div>

                                            <!-- Image Upload -->
                                            <div class="mb-3">
                                                <label for="section3_image" class="form-label">Upload Gambar (564 x 2030)</label>
                                                <input type="file" class="form-control" id="section3_image" name="section3_image" accept="image/*">
                                                <div class="form-text">Format yang didukung: JPG, PNG, GIF. Ukuran maksimal: 2MB</div>
                                                <div id="section3_image_preview" class="mt-2" style="display: none;">
                                                    <img id="section3_preview_img" src="" alt="Preview" class="img-thumbnail" style="max-width: 200px;">
                                                </div>
                                            </div>

                                            <!-- Content Description -->
                                            <div class="mb-3">
                                                <label for="section3_content" class="form-label">Keterangan</label>
                                                <div id="section3_content" style="height: 200px;"></div>
                                                <input type="hidden" id="section3_content_hidden" name="section3_content">
                                                <div class="form-text">Deskripsi konten untuk section ini</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Section 4 Content -->
                            <div class="tab-pane fade" id="v-pills-section4" role="tabpanel" aria-labelledby="v-pills-section4-tab" tabindex="0">
                                <h4 class="mb-4"><i class="bx bx-image me-2"></i> Landing Page Section 4 <span class="badge bg-secondary">Optional</span></h4>

                                <div class="card">
                                    <div class="card-body">
                                        <!-- Section 4 Enable Toggle -->
                                        <div class="alert alert-info d-flex align-items-center mb-4">
                                            <div class="form-check form-switch me-3">
                                                <input class="form-check-input" type="checkbox" id="section4_enabled" name="section4_enabled" onchange="toggleSectionFields(4)">
                                                <label class="form-check-label" for="section4_enabled">
                                                    <i class="bx bx-toggle-left me-1"></i>Aktifkan Section 4
                                                </label>
                                            </div>
                                            <div class="flex-grow-1">
                                                <small class="mb-0">Aktifkan untuk menampilkan section 4 di landing page</small>
                                            </div>
                                        </div>

                                        <div id="section4_fields">
                                            <h5 class="card-title"><i class="bx bx-package me-2"></i> Product</h5>

                                            <!-- Product Selection and Background Color -->
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="section4_product_id" class="form-label">Produk</label>
                                                        <input type="text" class="form-control" id="section4_product_display" placeholder="Produk akan mengikuti pilihan di Section 1" readonly>
                                                        <input type="hidden" id="section4_product_id" name="section4_product_id">
                                                        <div class="form-text">Produk akan otomatis mengikuti produk yang dipilih di Section 1</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <!-- Background Color -->
                                                    <div class="mb-3">
                                                        <label for="section4_background_color" class="form-label">Background Color</label>
                                                        <div class="input-group">
                                                            <input type="color" class="form-control form-control-color" id="section4_background_color" name="section4_background_color" value="#DCE8FD" title="Choose background color">
                                                            <input type="text" class="form-control" id="section4_background_color_text" value="#DCE8FD" readonly>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Section Title -->
                                            <div class="mb-3">
                                                <label for="section4_title" class="form-label">Judul Section</label>
                                                <input type="text" class="form-control" id="section4_title" name="section4_title" placeholder="Masukkan judul section">
                                            </div>

                                            <!-- Sub Section Title -->
                                            <div class="mb-3">
                                                <label for="section4_sub_title" class="form-label">Sub Judul Section</label>
                                                <input type="text" class="form-control" id="section4_sub_title" name="section4_sub_title" placeholder="Masukkan sub judul section">
                                            </div>

                                            <!-- Image Upload -->
                                            <div class="mb-3">
                                                <label for="section4_image" class="form-label">Upload Gambar (564 x 2030)</label>
                                                <input type="file" class="form-control" id="section4_image" name="section4_image" accept="image/*">
                                                <div class="form-text">Format yang didukung: JPG, PNG, GIF. Ukuran maksimal: 2MB</div>
                                                <div id="section4_image_preview" class="mt-2" style="display: none;">
                                                    <img id="section4_preview_img" src="" alt="Preview" class="img-thumbnail" style="max-width: 200px;">
                                                </div>
                                            </div>

                                            <!-- Content Description -->
                                            <div class="mb-3">
                                                <label for="section4_content" class="form-label">Keterangan</label>
                                                <div id="section4_content" style="height: 200px;"></div>
                                                <input type="hidden" id="section4_content_hidden" name="section4_content">
                                                <div class="form-text">Deskripsi konten untuk section ini</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Section 5 Content -->
                            <div class="tab-pane fade" id="v-pills-section5" role="tabpanel" aria-labelledby="v-pills-section5-tab" tabindex="0">
                                <h4 class="mb-4"><i class="bx bx-file me-2"></i> Landing Page Section 5 <span class="badge bg-secondary">Optional</span></h4>

                                <div class="card">
                                    <div class="card-body">
                                        <!-- Section 5 Enable Toggle -->
                                        <div class="alert alert-info d-flex align-items-center mb-4">
                                            <div class="form-check form-switch me-3">
                                                <input class="form-check-input" type="checkbox" id="section5_enabled" name="section5_enabled" onchange="toggleSectionFields(5)">
                                                <label class="form-check-label" for="section5_enabled">
                                                    <i class="bx bx-toggle-left me-1"></i>Aktifkan Section 5
                                                </label>
                                            </div>
                                            <div class="flex-grow-1">
                                                <small class="mb-0">Aktifkan untuk menampilkan section 5 di landing page</small>
                                            </div>
                                        </div>

                                        <div id="section5_fields">
                                            <h5 class="card-title"><i class="bx bx-package me-2"></i> Product</h5>

                                            <!-- Product Selection and Background Color -->
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="section5_product_id" class="form-label">Produk</label>
                                                        <input type="text" class="form-control" id="section5_product_display" placeholder="Produk akan mengikuti pilihan di Section 1" readonly>
                                                        <input type="hidden" id="section5_product_id" name="section5_product_id">
                                                        <div class="form-text">Produk akan otomatis mengikuti produk yang dipilih di Section 1</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <!-- Background Color -->
                                                    <div class="mb-3">
                                                        <label for="section5_background_color" class="form-label">Background Color</label>
                                                        <div class="input-group">
                                                            <input type="color" class="form-control form-control-color" id="section5_background_color" name="section5_background_color" value="#DCE8FD" title="Choose background color">
                                                            <input type="text" class="form-control" id="section5_background_color_text" value="#DCE8FD" readonly>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Section Title -->
                                            <div class="mb-3">
                                                <label for="section5_title" class="form-label">Judul Section</label>
                                                <input type="text" class="form-control" id="section5_title" name="section5_title" placeholder="Masukkan judul section">
                                            </div>

                                            <!-- Sub Section Title -->
                                            <div class="mb-3">
                                                <label for="section5_sub_title" class="form-label">Sub Judul Section</label>
                                                <input type="text" class="form-control" id="section5_sub_title" name="section5_sub_title" placeholder="Masukkan sub judul section">
                                            </div>

                                            <!-- Image Upload -->
                                            <div class="mb-3">
                                                <label for="section5_image" class="form-label">Upload Gambar (564 x 2030)</label>
                                                <input type="file" class="form-control" id="section5_image" name="section5_image" accept="image/*">
                                                <div class="form-text">Format yang didukung: JPG, PNG, GIF. Ukuran maksimal: 2MB</div>
                                                <div id="section5_image_preview" class="mt-2" style="display: none;">
                                                    <img id="section5_preview_img" src="" alt="Preview" class="img-thumbnail" style="max-width: 200px;">
                                                </div>
                                            </div>

                                            <!-- Content Description -->
                                            <div class="mb-3">
                                                <label for="section5_content" class="form-label">Keterangan</label>
                                                <div id="section5_content" style="height: 200px;"></div>
                                                <input type="hidden" id="section5_content_hidden" name="section5_content">
                                                <div class="form-text">Deskripsi konten untuk section ini</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Section 6 Content -->
                            <div class="tab-pane fade" id="v-pills-section6" role="tabpanel" aria-labelledby="v-pills-section6-tab" tabindex="0">
                                <h4 class="mb-4"><i class="bx bx-shopping-cart me-2"></i> Landing Page Section 6 - Form Order/Checkout</h4>

                                <!-- Product & Background Configuration -->
                                <div class="card mb-4">
                                    <div class="card-body">
                                        <h5 class="card-title"><i class="bx bx-cog me-2"></i> Konfigurasi Dasar</h5>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <!-- Product Selection -->
                                                <div class="mb-3">
                                                    <label for="section6_product_id" class="form-label">Pilih Produk untuk Checkout</label>
                                                    <select class="form-select" id="section6_product_id" name="section6_product_id">
                                                        <option value="">-- Pilih Produk (Opsional) --</option>
                                                        <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($product->id); ?>" data-price="<?php echo e($product->regular_price); ?>" data-discount="<?php echo e($product->discount_price); ?>">
                                                                <?php echo e($product->name); ?> - Rp <?php echo e(number_format($product->regular_price, 0, ',', '.')); ?>

                                                                <?php if($product->discount_price): ?>
                                                                    (Diskon: Rp <?php echo e(number_format($product->discount_price, 0, ',', '.')); ?>)
                                                                <?php endif; ?>
                                                            </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                    <div class="form-text">Produk yang akan ditampilkan di form checkout</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <!-- Background Color -->
                                                <div class="mb-3">
                                                    <label for="section6_background_color" class="form-label">Background Color <span class="text-danger">*</span></label>
                                                    <div class="input-group">
                                                        <input type="color" class="form-control form-control-color" id="section6_background_color" name="section6_background_color" value="#DCE8FD" title="Choose background color">
                                                        <input type="text" class="form-control" id="section6_background_color_text" value="#DCE8FD" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Customer Information Form Fields -->
                                <div class="card mb-4">
                                    <div class="card-body">
                                        <h5 class="card-title"><i class="bx bx-user me-2"></i>Form Pemesanan Customer</h5>

                                        <div class="row">
                                            <!-- Customer Name -->
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="section6_customer_name_label" class="form-label">Label Nama Anda <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="section6_customer_name_label" name="section6_customer_name_label" value="Nama Anda" placeholder="Masukkan label untuk nama">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <!-- Customer Name Placeholder -->
                                                <div class="mb-3">
                                                    <label for="section6_customer_name_placeholder" class="form-label">Placeholder Nama Anda</label>
                                                    <input type="text" class="form-control" id="section6_customer_name_placeholder" name="section6_customer_name_placeholder" value="Masukkan nama lengkap" placeholder="Masukkan placeholder">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <!-- WhatsApp Number -->
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="section6_whatsapp_label" class="form-label">Label No WhatsApp Anda <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="section6_whatsapp_label" name="section6_whatsapp_label" value="No WhatsApp Anda" placeholder="Masukkan label untuk WhatsApp">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <!-- WhatsApp Placeholder -->
                                                <div class="mb-3">
                                                    <label for="section6_whatsapp_placeholder" class="form-label">Placeholder No WhatsApp Anda</label>
                                                    <input type="text" class="form-control" id="section6_whatsapp_placeholder" name="section6_whatsapp_placeholder" value="08xxxxxxxxxx" placeholder="Masukkan placeholder">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <!-- Complete Address -->
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="section6_address_label" class="form-label">Label Alamat Lengkap <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="section6_address_label" name="section6_address_label" value="Alamat Lengkap" placeholder="Masukkan label untuk alamat">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <!-- Address Placeholder -->
                                                <div class="mb-3">
                                                    <label for="section6_address_placeholder" class="form-label">Placeholder Alamat Lengkap</label>
                                                    <input type="text" class="form-control" id="section6_address_placeholder" name="section6_address_placeholder" value="Jl. Contoh No. 123, RT/RW 01/02" placeholder="Masukkan placeholder">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Location & Notes Configuration -->
                                <div class="card mb-4">
                                    <div class="card-body">
                                        <h5 class="card-title"><i class="bx bx-map me-2"></i>Lokasi & Catatan</h5>

                                        <!-- City/District -->
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="section6_city_label" class="form-label">Label Nama Kota / Kecamatan <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="section6_city_label" name="section6_city_label" value="Nama Kota / Kecamatan" placeholder="Masukkan label untuk kota">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="section6_city_default" class="form-label">Pilihan Default Kota</label>
                                                    <select class="form-select" id="section6_city_default" name="section6_city_default">
                                                        <option value="">Pilih kota default</option>
                                                        <option value="jakarta">Jakarta</option>
                                                        <option value="bandung">Bandung</option>
                                                        <option value="surabaya">Surabaya</option>
                                                        <option value="medan">Medan</option>
                                                        <option value="semarang">Semarang</option>
                                                        <option value="yogyakarta">Yogyakarta</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Order Notes -->
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="section6_notes_label" class="form-label">Label Catatan Pemesanan</label>
                                                    <input type="text" class="form-control" id="section6_notes_label" name="section6_notes_label" value="Catatan Pemesanan" placeholder="Masukkan label untuk catatan">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <!-- Notes Placeholder -->
                                                <div class="mb-3">
                                                    <label for="section6_notes_placeholder" class="form-label">Placeholder Catatan Pemesanan</label>
                                                    <input type="text" class="form-control" id="section6_notes_placeholder" name="section6_notes_placeholder" value="Catatan khusus untuk pesanan (opsional)" placeholder="Masukkan placeholder">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Shipping & Payment Configuration -->
                                <div class="card mb-4">
                                    <div class="card-body">
                                        <h5 class="card-title"><i class="bx bx-truck me-2"></i>Pengiriman & Pembayaran</h5>

                                        <!-- Shipping Options -->
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="section6_shipping_label" class="form-label">Label Pengiriman <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="section6_shipping_label" name="section6_shipping_label" value="Pengiriman" placeholder="Masukkan label untuk pengiriman">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="section6_shipping_service_label" class="form-label">Label Pilih Layanan</label>
                                                    <input type="text" class="form-control" id="section6_shipping_service_label" name="section6_shipping_service_label" value="Pilih Layanan" placeholder="Masukkan label untuk layanan">
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Payment Method -->
                                        <div class="mb-3">
                                            <label for="section6_payment_label" class="form-label">Label Payment Method <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="section6_payment_label" name="section6_payment_label" value="Metode Pembayaran" placeholder="Masukkan label untuk metode pembayaran">
                                        </div>

                                        <!-- Payment Options -->
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="section6_enable_bank_transfer" name="section6_enable_bank_transfer" checked>
                                                    <label class="form-check-label" for="section6_enable_bank_transfer">
                                                        <i class="bx bx-credit-card me-1"></i>Aktifkan Bank Transfer
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="section6_enable_cod" name="section6_enable_cod" checked>
                                                    <label class="form-check-label" for="section6_enable_cod">
                                                        <i class="bx bx-money me-1"></i>Aktifkan COD (Bayar di Tempat)
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Order Summary & Pricing -->
                                <div class="card mb-4">
                                    <div class="card-body">
                                        <h5 class="card-title"><i class="bx bx-receipt me-2"></i>Ringkasan Pesanan & Harga</h5>

                                        <!-- Product Pricing -->
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="section6_product_price" class="form-label">Harga Produk (Rp)</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">Rp</span>
                                                        <input type="number" class="form-control" id="section6_product_price" name="section6_product_price" placeholder="155000" value="155000" min="0">
                                                    </div>
                                                    <div class="form-text">Harga produk yang akan ditampilkan</div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="section6_shipping_cost" class="form-label">Biaya Ongkir (Rp)</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">Rp</span>
                                                        <input type="number" class="form-control" id="section6_shipping_cost" name="section6_shipping_cost" placeholder="15000" value="15000" min="0">
                                                    </div>
                                                    <div class="form-text">Estimasi biaya pengiriman</div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="section6_total_price" class="form-label">Total Harga (Rp)</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">Rp</span>
                                                        <input type="number" class="form-control" id="section6_total_price" name="section6_total_price" placeholder="170000" value="170000" min="0" readonly>
                                                    </div>
                                                    <div class="form-text">Total otomatis dihitung</div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Order Button -->
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="section6_order_button_text" class="form-label">Text Tombol Pesan</label>
                                                    <input type="text" class="form-control" id="section6_order_button_text" name="section6_order_button_text" value="Beli Sekarang" placeholder="Masukkan text tombol">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <!-- Section 1 Button -->
                                                <div class="mb-3">
                                                    <label for="section6_section1_button_text" class="form-label">Text Tombol Scroll ke Section 1</label>
                                                    <input type="text" class="form-control" id="section6_section1_button_text" name="section6_section1_button_text" value="Lihat Detail Produk" placeholder="Masukkan text tombol">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!-- Wizard-Style Action Buttons -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-4">
                        <!-- Progress Indicator -->
                        <div class="wizard-progress mb-4">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="progress-step active" data-step="1">
                                    <div class="step-circle">1</div>
                                    <span class="step-label">Section 1</span>
                                </div>
                                <div class="progress-line"></div>
                                <div class="progress-step" data-step="2">
                                    <div class="step-circle">2</div>
                                    <span class="step-label">Section 2</span>
                                </div>
                                <div class="progress-line"></div>
                                <div class="progress-step optional" data-step="3">
                                    <div class="step-circle">3</div>
                                    <span class="step-label">Section 3</span>
                                </div>
                                <div class="progress-line"></div>
                                <div class="progress-step optional" data-step="4">
                                    <div class="step-circle">4</div>
                                    <span class="step-label">Section 4</span>
                                </div>
                                <div class="progress-line"></div>
                                <div class="progress-step optional" data-step="5">
                                    <div class="step-circle">5</div>
                                    <span class="step-label">Section 5</span>
                                </div>
                                <div class="progress-line"></div>
                                <div class="progress-step" data-step="6">
                                    <div class="step-circle">6</div>
                                    <span class="step-label">Checkout</span>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="left-actions">
                                <button type="button" class="btn btn-outline-secondary" id="cancelBtn">
                                    <i class="bx bx-x me-1"></i>Batal dan Kembali
                                </button>
                                
                            </div>

                            <div class="center-info">
                                <span class="current-section-info text-muted">
                                    <i class="bx bx-info-circle me-1"></i>
                                    <span id="current-section-text">Mengisi Section 1 - Landing Page</span>
                                </span>
                            </div>

                            <div class="right-actions">
                                <button type="button" class="btn btn-outline-primary me-2" id="prevBtn" style="display: none;">
                                    <i class="bx bx-chevron-left me-1"></i>Sebelumnya
                                </button>
                                <button type="button" class="btn btn-success me-2" id="saveAndContinueBtn">
                                    <i class="bx bx-save me-1"></i>Simpan & Lanjutkan
                                </button>
                                <button type="button" class="btn btn-primary" id="nextBtn">
                                    Selanjutnya<i class="bx bx-chevron-right ms-1"></i>
                                </button>
                                <button type="submit" class="btn btn-primary" id="finalSaveBtn" style="display: none;">
                                    <i class="bx bx-check me-1"></i>Selesai & Simpan
                                </button>
                            </div>
                        </div>

                        <!-- Section Status Indicators -->
                        <div class="section-status mt-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <i class="bx bx-check-circle text-success me-1"></i>
                                        <span id="completed-sections">0 dari 6 section selesai</span>
                                    </small>
                                </div>
                                <div class="col-md-6 text-end">
                                    <small class="text-muted">
                                        <i class="bx bx-time me-1"></i>
                                        <span id="auto-save-status">Auto-save: Aktif</span>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('style'); ?>
    <link href="https://cdn.jsdelivr.net/npm/quill@2.0.3/dist/quill.snow.css" rel="stylesheet" />
    <style>
        /* Sidebar Navigation Improvements */
        .sidebar-nav-container {
            position: sticky;
            top: 20px;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 12px;
            border: 1px solid #e7e7ff;
        }

        .section-number {
            background: #696cff;
            color: white;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 13px;
            font-weight: 600;
            flex-shrink: 0;
        }

        .section-title {
            font-weight: 500;
            font-size: 14px;
        }

        .optional-badge {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: 500;
        }

        .section-nav.active .section-number {
            background: white;
            color: #696cff;
        }

        .section-nav.active .optional-badge {
            background: rgba(255, 255, 255, 0.2) !important;
            color: white;
        }

        .section-nav {
            border-radius: 10px;
            border: 1px solid #e7e7ff;
            padding: 12px 16px;
            min-height: 50px;
        }

        .section-nav:hover {
            background-color: #f0f2ff;
            border-color: #696cff;
        }

        .section-nav.active {
            background-color: #696cff;
            border-color: #696cff;
            color: white;
        }

        /* Switch Improvements */
        .form-check-input:checked {
            background-color: #696cff;
            border-color: #696cff;
        }

        .form-check-input:focus {
            outline: none;
        }

        /* Validation error styles */
        .form-control.is-invalid {
            border-color: #dc3545;
        }

        .invalid-feedback {
            display: block;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #dc3545;
        }

        /* Disabled Section Styling */
        .disabled-section {
            opacity: 0.6;
            pointer-events: none;
            position: relative;
        }

        .disabled-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.7);
            z-index: 1;
            border-radius: 8px;
        }

        .disabled-field {
            background-color: #f8f9fa !important;
            color: #6c757d !important;
            border-color: #dee2e6 !important;
        }

        /* Alert styling improvements */
        .alert-info {
            background-color: #e7f3ff;
            border-color: #b8daff;
            color: #0c5460;
        }

        /* Wizard Progress Styles */
        .wizard-progress {
            position: relative;
        }

        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .step-label {
            font-size: 12px;
            margin-top: 8px;
            color: #6c757d;
            font-weight: 500;
            text-align: center;
            transition: all 0.3s ease;
        }

        .progress-step.active .step-circle {
            background: #696cff;
            color: white;
            border-color: #696cff;
            box-shadow: 0 0 0 4px rgba(105, 108, 255, 0.2);
        }

        .progress-step.active .step-label {
            color: #696cff;
            font-weight: 600;
        }

        .progress-step.completed .step-circle {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }

        .progress-step.completed .step-label {
            color: #28a745;
        }

        .progress-step.optional .step-label::after {
            content: " (Opsional)";
            font-size: 10px;
            color: #6c757d;
        }

        .progress-line {
            flex: 1;
            height: 2px;
            background: #e9ecef;
            margin: 0 10px;
            position: relative;
            top: -20px;
        }

        .progress-line.completed {
            background: #28a745;
        }

        /* Action Button Improvements */
        .left-actions,
        .right-actions {
            display: flex;
            align-items: center;
        }

        .center-info {
            flex: 1;
            text-align: center;
        }

        .current-section-info {
            font-size: 14px;
            font-weight: 500;
        }

        .btn {
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-success {
            background: #28a745;
            border-color: #28a745;
        }

        .btn-success:hover {
            background: #218838;
            border-color: #1e7e34;
        }

        /* Section Status Styling */
        .section-status {
            border-top: 1px solid #e9ecef;
            padding-top: 15px;
        }

        /* Auto-save indicator */
        #auto-save-status {
            position: relative;
        }

        .auto-save-active::before {
            content: '';
            width: 8px;
            height: 8px;
            background: #28a745;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }

            100% {
                opacity: 1;
            }
        }

        /* Disabled section styling */
        .disabled-section {
            opacity: 0.6;
            pointer-events: none;
        }

        .disabled-field {
            background-color: #f8f9fa !important;
            color: #6c757d !important;
            cursor: not-allowed !important;
        }

        .disabled-section .form-control,
        .disabled-section .form-select {
            background-color: #f8f9fa !important;
            color: #6c757d !important;
            cursor: not-allowed !important;
        }

        .disabled-section .ql-editor {
            background-color: #f8f9fa !important;
            color: #6c757d !important;
            cursor: not-allowed !important;
        }

        .disabled-section .form-control-color {
            opacity: 0.5;
            cursor: not-allowed !important;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .wizard-progress .d-flex {
                flex-wrap: wrap;
                gap: 10px;
            }

            .progress-line {
                display: none;
            }

            .d-flex.justify-content-between {
                flex-direction: column;
                gap: 15px;
            }

            .left-actions,
            .right-actions {
                justify-content: center;
            }

            .center-info {
                order: -1;
            }
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
    <script src="https://cdn.jsdelivr.net/npm/quill@2.0.3/dist/quill.js"></script>
    <script>
        $(document).ready(function() {
            // Load configuration from sessionStorage if available
            const landingPageConfig = sessionStorage.getItem('landingPageConfig');
            if (landingPageConfig) {
                try {
                    const config = JSON.parse(landingPageConfig);

                    // Populate hidden fields with pixel tracking configuration
                    $('#facebook_pixel_id_config').val(config.facebook_pixel_id || '');
                    $('#pixel_events_config').val(JSON.stringify(config.pixel_events || []));
                    $('#pixel_event_parameters_config').val(JSON.stringify(config.pixel_event_parameters || {}));

                    // Load product data and populate read-only fields
                    if (config.product_id) {
                        // Load product details from server
                        loadProductDetails(config.product_id);
                    }

                    // Clear sessionStorage after use
                    sessionStorage.removeItem('landingPageConfig');

                    // Update pixel status display
                    updatePixelStatusDisplay();

                    // Show success message
                    Swal.fire({
                        icon: 'success',
                        title: 'Konfigurasi Dimuat!',
                        text: 'Konfigurasi pixel tracking dan produk telah dimuat.',
                        timer: 2000,
                        showConfirmButton: false
                    });
                } catch (error) {
                    console.error('Error parsing landing page config:', error);
                    sessionStorage.removeItem('landingPageConfig');
                }
            } else {
                // If no config found, show default pixel status
                updatePixelStatusDisplay();

                // Redirect back to index
                Swal.fire({
                    icon: 'warning',
                    title: 'Konfigurasi Tidak Ditemukan!',
                    text: 'Silakan pilih produk terlebih dahulu dari halaman utama.',
                    confirmButtonColor: '#696cff'
                }).then(() => {
                    window.location.href = '<?php echo e(route('landingPage.index')); ?>';
                });
            }

            // Function to load product details
            function loadProductDetails(productId) {
                $.ajax({
                    url: '<?php echo e(route('landingPage.getProducts')); ?>',
                    method: 'GET',
                    success: function(response) {
                        if (response.success && response.data) {
                            const product = response.data.find(p => p.id == productId);
                            if (product) {
                                // Populate Section 1 product fields
                                const price = product.discount_price || product.regular_price;
                                const formattedPrice = new Intl.NumberFormat('id-ID', {
                                    style: 'currency',
                                    currency: 'IDR',
                                    minimumFractionDigits: 0
                                }).format(price);

                                const productDisplayText = `${product.name} - ${formattedPrice}`;
                                $('#product_display').val(productDisplayText);
                                $('#product_id').val(product.id);

                                // Auto-populate all other sections with the same product
                                const sections = ['section2', 'section3', 'section4', 'section5'];
                                sections.forEach(function(section) {
                                    $(`#${section}_product_display`).val(productDisplayText);
                                    $(`#${section}_product_id`).val(product.id);
                                });

                                // Also populate Section 6 if needed
                                $('#section6_product_id').val(product.id);

                                // Update Section 6 pricing
                                $('#section6_product_price').val(price);
                                const shippingCost = parseFloat($('#section6_shipping_cost').val()) || 0;
                                const total = price + shippingCost;
                                $('#section6_total_price').val(total);
                            }
                        }
                    },
                    error: function(xhr) {
                        console.error('Error loading product details:', xhr);
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: 'Gagal memuat detail produk.',
                            confirmButtonColor: '#696cff'
                        });
                    }
                });
            }

            // Initialize Quill editors
            const editors = {};
            const initializedEditors = new Set();

            console.log('Landing Page Builder: Starting initialization...');

            // Function to safely initialize a Quill editor
            function initializeQuillEditor(containerId, editorKey, options = {}) {
                try {
                    // Check if Quill is available
                    if (typeof Quill === 'undefined') {
                        console.error('Quill is not loaded');
                        return null;
                    }

                    const container = document.getElementById(containerId);
                    if (!container) {
                        console.warn(`Container ${containerId} not found`);
                        return null;
                    }

                    // Check if container is visible
                    const isVisible = container.offsetParent !== null;
                    if (!isVisible) {
                        console.warn(`Container ${containerId} is not visible, skipping initialization`);
                        return null;
                    }

                    // Check if already initialized
                    if (initializedEditors.has(editorKey)) {
                        return editors[editorKey];
                    }

                    const defaultOptions = {
                        theme: 'snow',
                        placeholder: options.placeholder || 'Masukkan konten...'
                    };

                    if (options.modules) {
                        defaultOptions.modules = options.modules;
                    }

                    const editor = new Quill(`#${containerId}`, defaultOptions);
                    editors[editorKey] = editor;
                    initializedEditors.add(editorKey);

                    // Store reference in the container element for easier access
                    container.__quill = editor;

                    console.log(`Successfully initialized Quill editor: ${editorKey}`);
                    return editor;
                } catch (error) {
                    console.error(`Error initializing Quill editor ${editorKey}:`, error);
                    return null;
                }
            }

            // Initialize the main content description editor (Section 1 - visible by default)
            initializeQuillEditor('content_description', 'content_description', {
                placeholder: 'Masukkan deskripsi konten...',
                modules: {
                    toolbar: [
                        ['bold', 'italic', 'underline', 'strike'],
                        ['blockquote', 'code-block'],
                        [{
                            'header': 1
                        }, {
                            'header': 2
                        }],
                        [{
                            'list': 'ordered'
                        }, {
                            'list': 'bullet'
                        }],
                        [{
                            'script': 'sub'
                        }, {
                            'script': 'super'
                        }],
                        [{
                            'indent': '-1'
                        }, {
                            'indent': '+1'
                        }],
                        [{
                            'direction': 'rtl'
                        }],
                        [{
                            'size': ['small', false, 'large', 'huge']
                        }],
                        [{
                            'header': [1, 2, 3, 4, 5, 6, false]
                        }],
                        [{
                            'color': []
                        }, {
                            'background': []
                        }],
                        [{
                            'font': []
                        }],
                        [{
                            'align': []
                        }],
                        ['clean'],
                        ['link', 'image']
                    ]
                }
            });

            // Function to initialize editors for a specific section when tab is shown
            function initializeSectionEditor(sectionNumber) {
                const editorKey = `section${sectionNumber}_content`;
                const containerId = `section${sectionNumber}_content`;

                if (!initializedEditors.has(editorKey)) {
                    // Wait a bit more for the tab transition to complete
                    setTimeout(() => {
                        initializeQuillEditor(containerId, editorKey, {
                            placeholder: `Masukkan konten section ${sectionNumber}...`
                        });
                    }, 50);
                }
            }

            // Initialize Section 2 editor on first tab switch (since it's commonly used)
            let section2Initialized = false;

            function ensureSection2Editor() {
                if (!section2Initialized) {
                    section2Initialized = true;
                    setTimeout(() => {
                        initializeSectionEditor(2);
                    }, 200);
                }
            }

            // Call this after a short delay to initialize section 2 editor
            setTimeout(ensureSection2Editor, 1000);

            // Initialize all section editors after page load to ensure they're ready for toggle
            setTimeout(() => {
                for (let i = 3; i <= 6; i++) {
                    const containerId = `section${i}_content`;
                    const editorKey = `section${i}_content`;

                    // Only initialize if container exists and not already initialized
                    if (document.getElementById(containerId) && !initializedEditors.has(editorKey)) {
                        const editor = initializeQuillEditor(containerId, editorKey, {
                            placeholder: `Masukkan konten section ${i}...`
                        });

                        if (editor) {
                            console.log(`Editor ${editorKey} initialized successfully for toggle functionality`);
                        }
                    }
                }

                // Initialize sections 3, 4, 5 as disabled by default after editors are ready
                setTimeout(() => {
                    for (let i = 3; i <= 5; i++) {
                        const checkbox = document.getElementById(`section${i}_enabled`);
                        if (checkbox && !checkbox.checked) {
                            toggleSectionFields(i);
                        }
                    }
                }, 500);
            }, 1500);

            // Color picker functionality
            $('#background_color').on('change', function() {
                $('#background_color_text').val($(this).val());
            });

            $('#section2_background_color').on('change', function() {
                $('#section2_background_color_text').val($(this).val());
            });

            // Section 3-6 color pickers
            for (let i = 3; i <= 6; i++) {
                $(`#section${i}_background_color`).on('change', function() {
                    $(`#section${i}_background_color_text`).val($(this).val());
                });
            }

            // Image preview functionality
            $('#section_image').on('change', function() {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        $('#preview_img').attr('src', e.target.result);
                        $('#image_preview').show();
                    };
                    reader.readAsDataURL(file);
                }
            });

            $('#section2_image').on('change', function() {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        $('#section2_preview_img').attr('src', e.target.result);
                        $('#section2_image_preview').show();
                    };
                    reader.readAsDataURL(file);
                }
            });

            // Section 3-6 image preview functionality
            for (let i = 3; i <= 6; i++) {
                $(`#section${i}_image`).on('change', function() {
                    const file = this.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            $(`#section${i}_preview_img`).attr('src', e.target.result);
                            $(`#section${i}_image_preview`).show();
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }

            // Function to clear validation errors
            function clearValidationErrors() {
                $('.form-control').removeClass('is-invalid');
                $('.invalid-feedback').remove();
            }

            // Function to show validation error for a field
            function showFieldError(fieldId, message) {
                const field = $(`#${fieldId}`);
                field.addClass('is-invalid');

                // Remove existing error message
                field.siblings('.invalid-feedback').remove();

                // Add error message
                field.after(`<div class="invalid-feedback">${message}</div>`);
            }

            // Custom form validation function
            function validateForm() {
                const errors = [];

                // Clear previous validation errors
                clearValidationErrors();

                // Section 1 validation (always required)
                const name = $('#name').val().trim();
                const mainTitle = $('#main_title').val().trim();
                const productId = $('#product_id').val();
                const sectionImage = $('#section_image')[0].files[0];

                if (!name) {
                    errors.push({
                        field: 'name',
                        message: 'Nama Landing Page harus diisi',
                        tab: 'v-pills-section1'
                    });
                    showFieldError('name', 'Nama Landing Page harus diisi');
                }

                if (!mainTitle) {
                    errors.push({
                        field: 'main_title',
                        message: 'Judul Landing Page harus diisi',
                        tab: 'v-pills-section1'
                    });
                    showFieldError('main_title', 'Judul Landing Page harus diisi');
                }

                if (!productId) {
                    errors.push({
                        field: 'product_display',
                        message: 'Produk harus dipilih',
                        tab: 'v-pills-section1'
                    });
                    showFieldError('product_display', 'Produk harus dipilih');
                }

                if (!sectionImage) {
                    errors.push({
                        field: 'section_image',
                        message: 'Gambar Section 1 harus diupload',
                        tab: 'v-pills-section1'
                    });
                    showFieldError('section_image', 'Gambar Section 1 harus diupload');
                }

                return errors;
            }

            // Form submission
            $('#landingPageForm').on('submit', function(e) {
                e.preventDefault();

                // Validate form
                const validationErrors = validateForm();

                if (validationErrors.length > 0) {
                    // Focus on first error field and switch to appropriate tab
                    const firstError = validationErrors[0];

                    // Switch to the tab containing the error
                    $(`#${firstError.tab}-tab`).tab('show');

                    // Focus on the field after tab switch
                    setTimeout(() => {
                        $(`#${firstError.field}`).focus();
                    }, 100);

                    // Show error message
                    const errorMessages = validationErrors.map(error => error.message).join('<br>');
                    Swal.fire({
                        icon: 'error',
                        title: 'Validasi Gagal!',
                        html: errorMessages,
                        confirmButtonColor: '#696cff'
                    });

                    return false;
                }

                // Update hidden fields with editor content
                Object.keys(editors).forEach(key => {
                    const hiddenField = $(`#${key}_hidden`);
                    if (hiddenField.length && editors[key]) {
                        try {
                            hiddenField.val(editors[key].root.innerHTML);
                        } catch (error) {
                            console.warn(`Error getting content from editor ${key}:`, error);
                        }
                    }
                });

                // Show loading state
                $('#saveBtn').prop('disabled', true).html('<i class="bx bx-loader-alt bx-spin me-1"></i>Menyimpan...');

                // Create FormData object for file uploads
                const formData = new FormData(this);

                // Handle checkbox values properly - ensure they are sent as boolean values
                const checkboxFields = ['section3_enabled', 'section4_enabled', 'section5_enabled', 'section6_enable_bank_transfer', 'section6_enable_cod'];
                checkboxFields.forEach(field => {
                    const checkbox = document.getElementById(field);
                    if (checkbox) {
                        // Remove the existing value and set proper boolean
                        formData.delete(field);
                        formData.append(field, checkbox.checked ? '1' : '0');
                    }
                });

                // Handle facebook_pixel_id - remove if empty
                const facebookPixelId = formData.get('facebook_pixel_id');
                if (!facebookPixelId || facebookPixelId.trim() === '') {
                    formData.delete('facebook_pixel_id');
                }

                // Handle pixel events data - convert JSON string back to array
                const pixelEventsValue = formData.get('pixel_events');
                const pixelEventParametersValue = formData.get('pixel_event_parameters');

                // Remove the original JSON string fields
                formData.delete('pixel_events');
                formData.delete('pixel_event_parameters');

                if (pixelEventsValue && pixelEventsValue.trim() !== '') {
                    try {
                        const pixelEventsArray = JSON.parse(pixelEventsValue);

                        // Add each event as a separate form field for array handling
                        if (Array.isArray(pixelEventsArray) && pixelEventsArray.length > 0) {
                            pixelEventsArray.forEach((event, index) => {
                                formData.append(`pixel_events[${index}]`, event);
                            });
                        }
                    } catch (error) {
                        console.warn('Error parsing pixel events:', error);
                        // Don't add anything if parsing fails
                    }
                }

                if (pixelEventParametersValue && pixelEventParametersValue.trim() !== '') {
                    try {
                        const pixelEventParametersObj = JSON.parse(pixelEventParametersValue);

                        // Add each parameter as a separate form field for object handling
                        if (typeof pixelEventParametersObj === 'object' && pixelEventParametersObj !== null) {
                            Object.keys(pixelEventParametersObj).forEach(key => {
                                formData.append(`pixel_event_parameters[${key}]`, pixelEventParametersObj[key]);
                            });
                        }
                    } catch (error) {
                        console.warn('Error parsing pixel event parameters:', error);
                        // Don't add anything if parsing fails
                    }
                }

                // Submit form via AJAX
                $.ajax({
                    url: $(this).attr('action'),
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Berhasil!',
                                text: response.message,
                                confirmButtonColor: '#696cff'
                            }).then(() => {
                                window.location.href = '<?php echo e(route('landingPage.index')); ?>';
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Gagal!',
                                text: response.message,
                                confirmButtonColor: '#696cff'
                            });
                        }
                    },
                    error: function(xhr) {
                        let errorMessage = 'Terjadi kesalahan saat menyimpan landing page.';

                        console.log('Error response:', xhr.responseJSON); // Debug log

                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                            const errors = xhr.responseJSON.errors;
                            const errorList = [];

                            // Create detailed error messages
                            Object.keys(errors).forEach(field => {
                                const fieldErrors = errors[field];
                                if (Array.isArray(fieldErrors)) {
                                    fieldErrors.forEach(error => {
                                        errorList.push(`<strong>${field}:</strong> ${error}`);
                                    });
                                } else {
                                    errorList.push(`<strong>${field}:</strong> ${fieldErrors}`);
                                }
                            });

                            errorMessage = errorList.join('<br>');
                        }

                        Swal.fire({
                            icon: 'error',
                            title: 'Gagal!',
                            html: errorMessage,
                            confirmButtonColor: '#696cff'
                        });
                    },
                    complete: function() {
                        $('#saveBtn').prop('disabled', false).html('<i class="bx bx-save me-1"></i>Simpan Landing Page');
                    }
                });
            });

            // Cancel button
            $('#cancelBtn').on('click', function() {
                Swal.fire({
                    title: 'Yakin ingin membatalkan?',
                    text: 'Semua perubahan yang belum disimpan akan hilang.',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'Ya, Batalkan',
                    cancelButtonText: 'Tidak'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = '<?php echo e(route('landingPage.index')); ?>';
                    }
                });
            });

            // Preview button
            $('#previewBtn').on('click', function() {
                // Update hidden fields with editor content
                Object.keys(editors).forEach(key => {
                    const hiddenField = $(`#${key}_hidden`);
                    if (hiddenField.length && editors[key]) {
                        try {
                            hiddenField.val(editors[key].root.innerHTML);
                        } catch (error) {
                            console.warn(`Error getting content from editor ${key}:`, error);
                        }
                    }
                });

                Swal.fire({
                    icon: 'info',
                    title: 'Preview',
                    text: 'Fitur preview akan segera tersedia.',
                    confirmButtonColor: '#696cff'
                });
            });

            // Tab change handler to update save button text and initialize editors
            $('button[data-bs-toggle="pill"]').on('shown.bs.tab', function(e) {
                const target = $(e.target).attr('data-bs-target');
                const sectionNumber = target.replace('#v-pills-section', '');
                $('#saveBtn').html(`<i class="bx bx-save me-1"></i>Simpan Section ${sectionNumber}`);

                // Initialize editor for the newly shown section (with a small delay to ensure DOM is ready)
                setTimeout(() => {
                    if (sectionNumber >= 2 && sectionNumber <= 6) {
                        initializeSectionEditor(sectionNumber);
                    }
                }, 100);
            });

            // Auto-populate pricing when product is selected in Section 6
            $('#section6_product_id').on('change', function() {
                const selectedOption = $(this).find('option:selected');
                const price = selectedOption.data('price');
                const discount = selectedOption.data('discount');

                if (price) {
                    const finalPrice = discount && discount > 0 ? discount : price;
                    $('#section6_product_price').val(finalPrice);

                    // Calculate total (product price + shipping cost)
                    const shippingCost = parseFloat($('#section6_shipping_cost').val()) || 0;
                    const total = finalPrice + shippingCost;
                    $('#section6_total_price').val(total);
                }
            });

            // Recalculate total when shipping cost changes
            $('#section6_shipping_cost').on('input', function() {
                const productPrice = parseFloat($('#section6_product_price').val()) || 0;
                const shippingCost = parseFloat($(this).val()) || 0;
                const total = productPrice + shippingCost;
                $('#section6_total_price').val(total);
            });

            // Recalculate total when product price changes
            $('#section6_product_price').on('input', function() {
                const productPrice = parseFloat($(this).val()) || 0;
                const shippingCost = parseFloat($('#section6_shipping_cost').val()) || 0;
                const total = productPrice + shippingCost;
                $('#section6_total_price').val(total);
            });

            // Clear validation errors when user starts typing/changing fields
            $('#name, #main_title, #product_display, #section_image').on('input change', function() {
                $(this).removeClass('is-invalid');
                $(this).siblings('.invalid-feedback').remove();
            });

            // Helper function to validate pixel configuration (optional but informative)
            function validatePixelConfiguration() {
                const pixelId = $('#facebook_pixel_id_config').val();
                const eventsValue = $('#pixel_events_config').val();
                const parametersValue = $('#pixel_event_parameters_config').val();

                let events = [];
                let parameters = {};

                try {
                    events = eventsValue ? JSON.parse(eventsValue) : [];
                } catch (e) {
                    events = [];
                }

                try {
                    parameters = parametersValue ? JSON.parse(parametersValue) : {};
                } catch (e) {
                    parameters = {};
                }

                // All levels are optional, return configuration summary
                if (!pixelId) {
                    return {
                        level: 'none',
                        message: 'Pixel tracking tidak aktif',
                        valid: true
                    };
                }

                if (events.length === 0) {
                    return {
                        level: 'basic',
                        message: 'Basic pixel tracking (tanpa custom events)',
                        valid: true
                    };
                }

                if (Object.keys(parameters).length === 0) {
                    return {
                        level: 'events',
                        message: `${events.length} event(s) akan ditrack tanpa parameters`,
                        valid: true
                    };
                }

                return {
                    level: 'advanced',
                    message: `${events.length} event(s) dengan parameters lengkap`,
                    valid: true
                };
            }

            // Function to update pixel status display
            function updatePixelStatusDisplay() {
                const config = validatePixelConfiguration();
                const statusElement = $('#pixel-status');

                // Define status styling based on configuration level
                const statusConfig = {
                    'none': {
                        badge: 'bg-secondary',
                        icon: 'bx-x-circle',
                        text: 'Tidak Aktif'
                    },
                    'basic': {
                        badge: 'bg-info',
                        icon: 'bx-check-circle',
                        text: 'Basic Tracking'
                    },
                    'events': {
                        badge: 'bg-warning',
                        icon: 'bx-target-lock',
                        text: 'Event Tracking'
                    },
                    'advanced': {
                        badge: 'bg-success',
                        icon: 'bx-badge-check',
                        text: 'Advanced Tracking'
                    }
                };

                const status = statusConfig[config.level];

                statusElement.html(`
                    <span class="badge ${status.badge} me-2">
                        <i class="bx ${status.icon} me-1"></i>${status.text}
                    </span>
                    <small class="text-muted">${config.message}</small>
                `);
            }

            // Note: Product selection is now handled by loadProductDetails function
            // since products are pre-selected from the previous modal

            // ===== WIZARD NAVIGATION FUNCTIONALITY =====
            let currentStep = 1;
            const totalSteps = 6;
            const completedSections = new Set();
            let autoSaveEnabled = true;

            // Section information mapping
            const sectionInfo = {
                1: {
                    name: 'Landing Page',
                    required: true
                },
                2: {
                    name: 'Section 2',
                    required: true
                },
                3: {
                    name: 'Section 3',
                    required: false
                },
                4: {
                    name: 'Section 4',
                    required: false
                },
                5: {
                    name: 'Section 5',
                    required: false
                },
                6: {
                    name: 'Checkout Form',
                    required: true
                }
            };

            // Initialize wizard
            function initializeWizard() {
                updateProgressIndicator();
                updateCurrentSectionInfo();
                updateNavigationButtons();
                updateCompletedSectionsCount();

                // Auto-save status
                $('#auto-save-status').addClass('auto-save-active');
            }

            // Update progress indicator
            function updateProgressIndicator() {
                $('.progress-step').each(function() {
                    const step = parseInt($(this).data('step'));
                    const $this = $(this);

                    $this.removeClass('active completed');

                    if (step === currentStep) {
                        $this.addClass('active');
                    } else if (completedSections.has(step)) {
                        $this.addClass('completed');
                    }
                });

                // Update progress lines
                $('.progress-line').each(function(index) {
                    const $this = $(this);
                    const lineStep = index + 1; // Lines are between steps

                    if (completedSections.has(lineStep) && completedSections.has(lineStep + 1)) {
                        $this.addClass('completed');
                    } else {
                        $this.removeClass('completed');
                    }
                });
            }

            // Update current section info
            function updateCurrentSectionInfo() {
                const sectionName = sectionInfo[currentStep].name;
                const isRequired = sectionInfo[currentStep].required;
                const requiredText = isRequired ? '' : ' (Opsional)';

                $('#current-section-text').text(`Mengisi Section ${currentStep} - ${sectionName}${requiredText}`);
            }

            // Update navigation buttons
            function updateNavigationButtons() {
                // Previous button
                if (currentStep === 1) {
                    $('#prevBtn').hide();
                } else {
                    $('#prevBtn').show();
                }

                // Next button vs Final Save button
                if (currentStep === totalSteps) {
                    $('#nextBtn').hide();
                    $('#finalSaveBtn').show();
                    $('#saveAndContinueBtn').text('Simpan Section 6');
                } else {
                    $('#nextBtn').show();
                    $('#finalSaveBtn').hide();
                    $('#saveAndContinueBtn').text('Simpan & Lanjutkan');
                }
            }

            // Update completed sections count
            function updateCompletedSectionsCount() {
                const completed = completedSections.size;
                $('#completed-sections').text(`${completed} dari ${totalSteps} section selesai`);
            }

            // Navigate to specific section
            function navigateToSection(sectionNumber) {
                if (sectionNumber < 1 || sectionNumber > totalSteps) return;

                currentStep = sectionNumber;

                // Trigger the tab change
                $(`#v-pills-section${sectionNumber}-tab`).tab('show');

                // Update wizard UI
                updateProgressIndicator();
                updateCurrentSectionInfo();
                updateNavigationButtons();

                // Smooth scroll to top
                $('html, body').animate({
                    scrollTop: 0
                }, 300);
            }

            // Validate current section
            function validateCurrentSection() {
                clearValidationErrors();

                if (currentStep === 1) {
                    // Section 1 validation
                    const name = $('#name').val().trim();
                    const mainTitle = $('#main_title').val().trim();
                    const productId = $('#product_id').val();
                    const sectionImage = $('#section_image')[0].files[0];

                    let isValid = true;

                    if (!name) {
                        showFieldError('name', 'Nama landing page wajib diisi');
                        isValid = false;
                    }

                    if (!mainTitle) {
                        showFieldError('main_title', 'Judul landing page wajib diisi');
                        isValid = false;
                    }

                    if (!productId) {
                        showFieldError('product_display', 'Produk wajib dipilih');
                        isValid = false;
                    }

                    if (!sectionImage && !completedSections.has(1)) {
                        showFieldError('section_image', 'Gambar section wajib diupload');
                        isValid = false;
                    }

                    return isValid;
                }

                // For other sections, basic validation
                return true;
            }

            // Save current section
            function saveCurrentSection(showSuccessMessage = true) {
                if (!validateCurrentSection()) {
                    return false;
                }

                // Update editor content to hidden fields
                Object.keys(editors).forEach(key => {
                    const hiddenField = $(`#${key}_hidden`);
                    if (hiddenField.length && editors[key]) {
                        try {
                            hiddenField.val(editors[key].root.innerHTML);
                        } catch (error) {
                            console.warn(`Error getting content from editor ${key}:`, error);
                        }
                    }
                });

                // Mark section as completed
                completedSections.add(currentStep);
                updateProgressIndicator();
                updateCompletedSectionsCount();

                if (showSuccessMessage) {
                    // Show success toast
                    Swal.fire({
                        icon: 'success',
                        title: 'Tersimpan!',
                        text: `Section ${currentStep} berhasil disimpan`,
                        timer: 1500,
                        showConfirmButton: false,
                        toast: true,
                        position: 'top-end'
                    });
                }

                return true;
            }

            // Button event handlers
            $('#nextBtn').on('click', function() {
                if (saveCurrentSection()) {
                    navigateToSection(currentStep + 1);
                }
            });

            $('#prevBtn').on('click', function() {
                navigateToSection(currentStep - 1);
            });

            $('#saveAndContinueBtn').on('click', function() {
                if (saveCurrentSection()) {
                    if (currentStep < totalSteps) {
                        navigateToSection(currentStep + 1);
                    }
                }
            });

            // Progress step click handlers
            $('.progress-step').on('click', function() {
                const targetStep = parseInt($(this).data('step'));

                // Allow navigation to completed sections or next section
                if (completedSections.has(targetStep) || targetStep === currentStep + 1 || targetStep === currentStep - 1) {
                    if (targetStep !== currentStep) {
                        if (saveCurrentSection(false)) {
                            navigateToSection(targetStep);
                        }
                    }
                } else {
                    Swal.fire({
                        icon: 'info',
                        title: 'Navigasi Terbatas',
                        text: 'Selesaikan section saat ini terlebih dahulu sebelum melanjutkan.',
                        confirmButtonColor: '#696cff'
                    });
                }
            });

            // Tab change handler for wizard
            $('button[data-bs-toggle="pill"]').on('shown.bs.tab', function(e) {
                const target = $(e.target).attr('data-bs-target');
                const sectionNumber = parseInt(target.replace('#v-pills-section', ''));

                if (sectionNumber !== currentStep) {
                    currentStep = sectionNumber;
                    updateProgressIndicator();
                    updateCurrentSectionInfo();
                    updateNavigationButtons();
                }

                // Initialize editor for the newly shown section
                setTimeout(() => {
                    if (sectionNumber >= 2 && sectionNumber <= 6) {
                        initializeSectionEditor(sectionNumber);
                    }
                }, 100);
            });

            // Auto-save functionality
            let autoSaveTimeout;

            function triggerAutoSave() {
                if (!autoSaveEnabled) return;

                clearTimeout(autoSaveTimeout);
                autoSaveTimeout = setTimeout(() => {
                    if (validateCurrentSection()) {
                        saveCurrentSection(false);

                        // Show auto-save indicator
                        $('#auto-save-status').text('Auto-save: Tersimpan').removeClass('auto-save-active');
                        setTimeout(() => {
                            $('#auto-save-status').text('Auto-save: Aktif').addClass('auto-save-active');
                        }, 2000);
                    }
                }, 3000); // Auto-save after 3 seconds of inactivity
            }

            // Bind auto-save to form inputs
            $(document).on('input change', 'input, textarea, select', function() {
                if ($(this).closest('.tab-pane').hasClass('show active')) {
                    triggerAutoSave();
                }
            });

            // Initialize wizard after page load
            setTimeout(initializeWizard, 1000);

            // Initialize optional sections as disabled by default
            setTimeout(() => {
                // Disable sections 3, 4, and 5 by default
                for (let i = 3; i <= 5; i++) {
                    const checkbox = document.getElementById(`section${i}_enabled`);
                    if (checkbox && !checkbox.checked) {
                        toggleSectionFields(i);
                    }
                }
            }, 1500);
        });

        // Function to toggle section fields enabled/disabled state
        function toggleSectionFields(sectionNumber) {
            console.log(`Toggling section ${sectionNumber}`);

            const checkbox = document.getElementById(`section${sectionNumber}_enabled`);
            const fieldsContainer = document.getElementById(`section${sectionNumber}_fields`);
            const isEnabled = checkbox.checked;

            console.log(`Section ${sectionNumber} - Enabled: ${isEnabled}`);

            // Get all form inputs within the section
            const inputs = fieldsContainer.querySelectorAll('input, select, textarea');
            const quillEditors = fieldsContainer.querySelectorAll('.ql-editor');

            // Enable/disable all form inputs
            inputs.forEach(input => {
                if (input.type !== 'hidden') {
                    input.disabled = !isEnabled;
                    if (isEnabled) {
                        input.classList.remove('disabled-field');
                    } else {
                        input.classList.add('disabled-field');
                        // Clear values when disabled
                        if (input.type === 'file') {
                            input.value = '';
                        } else if (input.type === 'color') {
                            input.value = '#DCE8FD';
                        } else if (input.tagName === 'SELECT') {
                            input.selectedIndex = 0;
                        } else if (input.type !== 'hidden') {
                            input.value = '';
                        }
                    }
                }
            });

            // Handle Quill editors specifically for this section
            const sectionEditorKey = `section${sectionNumber}_content`;
            console.log(`Looking for editor: ${sectionEditorKey}`);

            if (editors[sectionEditorKey]) {
                console.log(`Found editor in editors object: ${sectionEditorKey}`);
                const quillInstance = editors[sectionEditorKey];
                quillInstance.enable(isEnabled);
                if (!isEnabled) {
                    quillInstance.setContents([]);
                    // Also clear the hidden field
                    const hiddenField = document.getElementById(`${sectionEditorKey}_hidden`);
                    if (hiddenField) {
                        hiddenField.value = '';
                    }
                }
            } else {
                console.log(`Editor not found in editors object, trying container: ${sectionEditorKey}`);
                // If editor not found in our editors object, try to find it by container
                const editorContainer = document.getElementById(sectionEditorKey);
                if (editorContainer) {
                    console.log(`Found editor container: ${sectionEditorKey}`);
                    // Try to get Quill instance from the container
                    const quillInstance = editorContainer.__quill;
                    if (quillInstance) {
                        console.log(`Found Quill instance in container: ${sectionEditorKey}`);
                        quillInstance.enable(isEnabled);
                        if (!isEnabled) {
                            quillInstance.setContents([]);
                            // Also clear the hidden field
                            const hiddenField = document.getElementById(`${sectionEditorKey}_hidden`);
                            if (hiddenField) {
                                hiddenField.value = '';
                            }
                        }
                    } else {
                        console.warn(`No Quill instance found for: ${sectionEditorKey}`);
                    }
                } else {
                    console.warn(`No editor container found for: ${sectionEditorKey}`);
                }
            }

            // Update visual appearance
            if (isEnabled) {
                fieldsContainer.classList.remove('disabled-section');
            } else {
                fieldsContainer.classList.add('disabled-section');
            }
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('templates.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\jualinn\resources\views/landing-page/create.blade.php ENDPATH**/ ?>